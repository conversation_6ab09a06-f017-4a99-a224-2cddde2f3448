:root {
    --slidev-theme-primary: #404040;
    /* --slidev-theme-secondary: #E60000; */
    --slidev-theme-secondary: #09BED9;
    counter-reset: h2counter;
    font-family: AlibabaPuHuiTi-Thin, sans-serif;
    --slidev-background-color: #fcfcfc;

    .abs {
        position: absolute;
    }
}

.slidev-layout {
    text-justify: inter-character;
    @apply h-full text-left text-$slidev-theme-primary;

    p+h2,
    ul+h2,
    table+h2 {
        @apply mt-10;
    }

    h1 {
        font-size: 6vw;
        color: var(--slidev-theme-secondary);
        margin: 9vw 0;
    }


    h2 {
        text-align: left;
        font-size: 5vw;
        margin: 5vw 1vw;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 20%);
        border-left: .5vw solid color-mix(in srgb, var(--slidev-theme-secondary), #000 20%);
        padding: 0.5vw 1vw;
        line-height: 5vw;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .05), 0 2px 5px 0 rgba(0, 0, 0, .03), 0 3px 1px -2px rgba(0, 0, 0, .01);
    }

    h3 {
        text-align: left;
        font-size: 4vw;
        margin: 6vw 1vw;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 20%);
        border-left: .3vw solid color-mix(in srgb, var(--slidev-theme-secondary), #000 25%);
        padding: 0.5vw;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .05), 0 2px 5px 0 rgba(0, 0, 0, .03), 0 3px 1px -2px rgba(0, 0, 0, .01);

    }

    h4 {
        text-align: left;
        font-size: 3.5vw;
        margin: 5vw 1vw;
        border-left: 2px solid color-mix(in srgb, var(--slidev-theme-secondary), #000 30%);
        padding: 0.2em 0.2em 0.2em 0.5em;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 30%);
    }

    a {
        color: #0000e6;
    }

    p {
        font-size: 3.3vw;
        margin: 3.5vw 1vw;
        line-height: 5.2vw;
        text-align: justify;
        font-weight: 100;
    }

    .admonition p {
        font-size: 2.8vw !important;
        line-height: 4vw;
    }

    .admonition li {
        font-size: 2.5vw !important;
        margin-top: 2.5vw;
    }

    li {
        font-size: 2.5vw;
    }

    img {
        /* 相当于上下各一行 */
        margin: 6vw auto !important;
    }

    .takeaway {
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14);
        position: relative;
        margin: 2vw 0;
        padding: 5vw 1vw 1vw 1vw;
        border-radius: .2rem;
        overflow: auto;
        background-color: rgba(255, 255, 255, 0.05);
        border-left: .4vw solid color-mix(in srgb, var(--slidev-theme-secondary), #000 15%);

        &:before {
            position: absolute;
            left: 0;
            top: 0;
            font-size: 2vw;
            height: 3vw;
            display: block;
            content: "\00a0\00a0" attr(title);
            font-weight: 400;
            width: 100%;

            border-bottom: 1px solid color-mix(in srgb, var(--slidev-theme-secondary), #000 15%);
            background-color: color-mix(in srgb, var(--slidev-theme-secondary), #000 15%);
        }
    }

    red {
        display: inline;
        color: color-mix(in srgb, #E60000, #000 15%);
    }

    strong {
        display: inline;
        font-weight: 500;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 30%);
    }

    claimer {
        display: block;
        text-align: center;
        color: #a8a8a8;
    }

    cap {
        display: inline-block;
        width: 100%;
        text-align: center;
        font-size: 2vw;
        color: grey;
        font-style: italic;
        position: relative;
        top: -2em;
    }

    /* .katex {
        width: 1000px;
    } */
}

.slidev-layout.intro {
    @apply h-full grid;

    h1 {
        color: inherit;
        @apply text-6xl leading-20;
    }
}


.slidev-layout.statement {
    @apply text-left h-full;
}

.slidev-layout.quote {
    @apply h-full;

    h1+p {
        @apply mt-2;
    }
}

.slidev-layout.section {
    h1 {
        @apply text-5xl font-500 leading-20 ml-10 mb-1;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-secondary), white) 1;
    }

    h1:after {
        content: unset;
    }

    h2 {
        @apply text-2xl font-300 ml-10;
    }

    h2:before {
        content: unset;
    }
}

.slidev-layout.center {

    h1,
    h2,
    h3,
    p {
        @apply text-center;
    }

    h1:after,
    h2:before,
    h3:before {
        content: unset;
    }

    h1 {
        @apply text-6xl font-500 leading-20 ml-10 mb-6 text-center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to left, white 0%, var(--slidev-theme-primary) 50%, white 100%)1;
    }
}
