## 0.21.3 (2022-07-19)


### Bug Fixes

* **seriph:** correct boldface and italics ([#10](https://github.com/slidevjs/themes/issues/10)) ([30353e1](https://github.com/slidevjs/themes/commit/30353e1ca1e638a5d5e0f5ba9809f414ade8b403))
* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* handleBackground helper colors all themes ([#6](https://github.com/slidevjs/themes/issues/6)) ([60a38f2](https://github.com/slidevjs/themes/commit/60a38f253b05df7c8408de84289867914411345a))
* remove `defineProps` imports ([9c02acf](https://github.com/slidevjs/themes/commit/9c02acf6353edfc81d88a5255306f126d06f148d))
* subheader style ([25491db](https://github.com/slidevjs/themes/commit/25491db815ac89f1cc1a952751e918e808164a48))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* new themes ([#3](https://github.com/slidevjs/themes/issues/3)) ([cbd0a1a](https://github.com/slidevjs/themes/commit/cbd0a1ac29ab4e7c5d5be24c55ee90a3945245d3))
* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.21.2 (2022-03-05)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* handleBackground helper colors all themes ([#6](https://github.com/slidevjs/themes/issues/6)) ([60a38f2](https://github.com/slidevjs/themes/commit/60a38f253b05df7c8408de84289867914411345a))
* remove `defineProps` imports ([9c02acf](https://github.com/slidevjs/themes/commit/9c02acf6353edfc81d88a5255306f126d06f148d))
* subheader style ([25491db](https://github.com/slidevjs/themes/commit/25491db815ac89f1cc1a952751e918e808164a48))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* new themes ([#3](https://github.com/slidevjs/themes/issues/3)) ([cbd0a1a](https://github.com/slidevjs/themes/commit/cbd0a1ac29ab4e7c5d5be24c55ee90a3945245d3))
* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.21.1 (2021-11-22)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* remove `defineProps` imports ([9c02acf](https://github.com/slidevjs/themes/commit/9c02acf6353edfc81d88a5255306f126d06f148d))
* subheader style ([25491db](https://github.com/slidevjs/themes/commit/25491db815ac89f1cc1a952751e918e808164a48))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* new themes ([#3](https://github.com/slidevjs/themes/issues/3)) ([cbd0a1a](https://github.com/slidevjs/themes/commit/cbd0a1ac29ab4e7c5d5be24c55ee90a3945245d3))
* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.21.0 (2021-09-13)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* remove `defineProps` imports ([9c02acf](https://github.com/slidevjs/themes/commit/9c02acf6353edfc81d88a5255306f126d06f148d))
* subheader style ([25491db](https://github.com/slidevjs/themes/commit/25491db815ac89f1cc1a952751e918e808164a48))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* new themes ([#3](https://github.com/slidevjs/themes/issues/3)) ([cbd0a1a](https://github.com/slidevjs/themes/commit/cbd0a1ac29ab4e7c5d5be24c55ee90a3945245d3))
* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.20.0 (2021-09-07)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* subheader style ([25491db](https://github.com/slidevjs/themes/commit/25491db815ac89f1cc1a952751e918e808164a48))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* new themes ([#3](https://github.com/slidevjs/themes/issues/3)) ([cbd0a1a](https://github.com/slidevjs/themes/commit/cbd0a1ac29ab4e7c5d5be24c55ee90a3945245d3))
* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.19.1 (2021-06-06)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* subheader style ([25491db](https://github.com/slidevjs/themes/commit/25491db815ac89f1cc1a952751e918e808164a48))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.19.0 (2021-06-04)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* use webfonts auto importing ([f3091e2](https://github.com/slidevjs/themes/commit/f3091e2f4fac1137bafaa841050690b956f607a4))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.17.0 (2021-06-03)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* update theme ([5ce60c6](https://github.com/slidevjs/themes/commit/5ce60c6a139a497acde5f4cbceb456854599f4f9))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.14.1 (2021-05-27)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.14.0 (2021-05-27)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* upgrade to slidev 0.14 ([5b97e29](https://github.com/slidevjs/themes/commit/5b97e29c45c51ef724252df2b711d1b30c7208cd))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.10 (2021-05-27)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.9 (2021-05-27)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.8 (2021-05-27)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.7 (2021-05-27)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.6 (2021-05-26)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.5 (2021-05-22)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.4 (2021-05-22)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))
* windicss extract glob not valid ([#1](https://github.com/slidevjs/themes/issues/1)) ([875fcc9](https://github.com/slidevjs/themes/commit/875fcc9c8ac6edf75fc49f7640a56acb9d438d2f))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.3 (2021-05-22)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))


### Features

* primary color customization ([df299cf](https://github.com/slidevjs/themes/commit/df299cf7c06fbc556fead0e11feeaf58142d5a20))
* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.1 (2021-05-20)


### Bug Fixes

* embbed layout helper ([ee4cd9a](https://github.com/slidevjs/themes/commit/ee4cd9a1456da59ddb8baafb6a4783f94200f42c))


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.1 (2021-05-15)


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



## 0.7.1 (2021-05-13)


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.7.0 (2021-05-13)


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))
* **style:** update slidev code background using prism values ([3c9da06](https://github.com/slidevjs/themes/commit/3c9da061865d15ea40efffd550b8c1ccbcd95c61))



# 0.7.0 (2021-05-11)


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))



# 0.7.0 (2021-05-11)


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))



# 0.7.0 (2021-05-11)


### Features

* **seriph:** new layouts ([2cd898b](https://github.com/slidevjs/themes/commit/2cd898b207f1de0aa593ec442f2cd4e12f0eafe8))



## 0.6.7 (2021-05-10)



## 0.6.7 (2021-05-10)



## 0.6.7 (2021-05-10)



