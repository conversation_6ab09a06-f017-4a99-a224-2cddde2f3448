@import 'prism-theme-vars/base.css';
@import 'codemirror-theme-vars/base.css';
@import 'prism-theme-vars/to-codemirror.css';

:root {
  --prism-font-family: var(--slidev-code-font-family);
  --slidev-code-background: var(--prism-background);
}

html:not(.dark) {
  --prism-foreground: #393a34;
  --prism-background: #fafafa;
  --prism-comment: #a0ada0;
  --prism-string: #b56959;
  --prism-literal: #2f8a89;
  --prism-number: #296aa3;
  --prism-keyword: #1c6b48;
  --prism-function: #6c7834;
  --prism-boolean: #1c6b48;
  --prism-constant: #a65e2b;
  --prism-deleted: #a14f55;
  --prism-class: #2993a3;
  --prism-builtin: #ab5959;
  --prism-property: #b58451;
  --prism-namespace: #b05a78;
  --prism-punctuation: #8e8f8b;
  --prism-decorator: #bd8f8f;
  --prism-regex: #ab5e3f;
  --prism-json-property: #698c96;
}

html.dark {
  --prism-foreground: #d4cfbf;
  --prism-background: #1b1b1b;
  --prism-comment: #758575;
  --prism-string: #d48372;
  --prism-literal: #429988;
  --prism-keyword: #4d9375;
  --prism-boolean: #1c6b48;
  --prism-number: #6394bf;
  --prism-variable: #c2b36e;
  --prism-function: #a1b567;
  --prism-deleted: #a14f55;
  --prism-class: #54b1bf;
  --prism-builtin: #e0a569;
  --prism-property: #dd8e6e;
  --prism-namespace: #db889a;
  --prism-punctuation: #858585;
  --prism-decorator: #bd8f8f;
  --prism-regex: #ab5e3f;
  --prism-json-property: #6b8b9e;
  --prism-line-number: #888888;
  --prism-line-number-gutter: #eeeeee;
  --prism-line-highlight-background: #444444;
  --prism-selection-background: #444444;
}

:not(pre) > code {
  font-size: 0.9em;
  background: var(--prism-background);
  @apply font-light py-0.5;
}

:not(pre) > code:before,
:not(pre) > code:after {
  content: '`';
  opacity: 0.50;
}

:not(pre) > code:before {
  margin-right: -0.08em;
}
