.slidev-layout {
    ol {
        padding: 0;
        margin-left: 4vw;
        text-align: left;
    }

    img {
        width: calc(100%);
        margin: 0 auto;
        display: block;
    }

    img[alt="25%"] {
        width: 25%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="33%"] {
        width: 33%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="50%"] {
        width: 50%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="66%"] {
        width: 66%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="75%"] {
        width: 75%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }


    img[alt="100%"] {
        width: 75%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="R50"] {
        position: relative;
        float: right;
        width: 45%;
        margin: 0 2vw;
    }

    img[alt="R33"] {
        position: relative;
        float: right;
        width: 30%;
        margin: 0 2vw;
    }

    img[alt="L50"] {
        position: relative;
        float: right;
        width: 45%;
        margin: 0 2vw;
    }

    img[alt="L33"] {
        position: relative;
        float: right;
        width: 30%;
        margin: 0 2vw;
    }
}

/*https: //github.com/slidevjs/slidev/issues/379*/
.fade {
    h2.slidev-vclick-target {
        transition: all 500ms ease;
        @apply text-4xl;
    }

    h2.slidev-vclick-hidden {
        @apply !opacity-50;
        @apply text-3xl;
    }

    h2.slidev-vclick-prior {
        @apply !opacity-50;
        @apply text-3xl;
    }
}

.fade {
    h3.slidev-vclick-target {
        transition: all 500ms ease;
        @apply text-3xl;
    }

    h2.slidev-vclick-hidden {
        @apply !opacity-50;
        @apply text-2xl;
    }

    h2.slidev-vclick-prior {
        @apply !opacity-50;
        @apply text-2xl;
    }

}

.fade {
    h4.slidev-vclick-target {
        transition: all 500ms ease;
        @apply text-2xl;
    }

    h4.slidev-vclick-hidden {
        @apply !opacity-50;
        @apply text-1xl;
    }

    h4.slidev-vclick-prior {
        @apply !opacity-50;
        @apply text-1xl;
    }

}

.fade {
    p.slidev-vclick-target {
        transition: all 500ms ease;
        font-size: 120%;
    }

    h4.slidev-vclick-hidden {
        @apply !opacity-50;
        font-size: 100%;
    }

    h4.slidev-vclick-prior {
        @apply !opacity-50;
        font-size: 100%
    }
}

.fade {
    li.slidev-vclick-target {
        transition: all 500ms ease;
        font-size: 120%;
    }

    h4.slidev-vclick-hidden {
        @apply !opacity-70;
        font-size: 100%;
    }

    h4.slidev-vclick-prior {
        @apply !opacity-70;
        font-size: 100%
    }
}

.line.slidev-vclick-target.dishonored {
    font-size: unset;
    @apply !opacity-60;
}

.line.slidev-vclick-target.highlighted {
    font-size: 1rem;
}

.line:hover {
    border-radius: 10px;
    border-width: 3px;
    box-shadow: 3px 3px rgba(0, 0, 0, 0.3);
    border-color: red;
    padding: 5px;
}
