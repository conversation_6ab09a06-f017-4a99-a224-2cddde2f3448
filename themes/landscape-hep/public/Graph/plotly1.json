{"data": [{"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 0, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#636efa", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "EN ECAL         ΔT>10ns: 18.08%, ΔT>40ns: 7.83 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "72bc9e", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.6773938759233503, 0.03148795413709384, 0.022478110493913066, 0.017632677683553467, 0.014547286721442454, 0.012414550290859541, 0.010743019625540234, 0.009481584053793025, 0.008462646945071301, 0.007618463977133065, 0.006960628300361994, 0.006393370411588594, 0.005893050365030502, 0.0055393080633234, 0.005155272355613739, 0.0048496668304997236, 0.0045768749614716765, 0.004359276569270258, 0.004114308261163032, 0.003965462291236974, 0.003775233814941363, 0.00361671008469502, 0.003474164739977482, 0.0033268813251031374, 0.0032195690765516385, 0.0031081740228650193, 0.002997535044203422, 0.002903832146102325, 0.0028004010826792737, 0.002725650465205402, 0.0026648116281919407, 0.0025669755199540546, 0.002498071882673689, 0.002449783891075598, 0.002368984673401548, 0.002319738986771762, 0.0022700396551269624, 0.0022250279886373014, 0.002180923612177667, 0.002124117175297658, 0.0020578850031057042, 0.0020376221924351063, 0.001972196500269843, 0.0019502199195425283, 0.0019144323683581393, 0.0018822739772938572, 0.0018403874209076217, 0.0017951237294096198, 0.0017817664039675592, 0.001742652122673072, 0.0017159878767906188, 0.0016734460553826972, 0.0016377089091999762, 0.0016174460985293787, 0.001601114877988897, 0.0015675451468779066, 0.0015501050163007255, 0.0015480384112323312, 0.0015223318603815729, 0.0014805461139986737, 0.0014581662932580135, 0.0014315524523772283, 0.0014109872116966219, 0.001408416556611546, 0.0013856334958575406, 0.0013473760995914121, 0.0013392608943228392, 0.001343394104459628, 0.0013041286081601363, 0.0012742888471725894, 0.001259419371680484, 0.001246263666245096, 0.00122786584063622, 0.0012234806054910907, 0.0011932376044901986, 0.0011777632689780753, 0.001170404138734525, 0.0011552322332324108, 0.0011403627577403054, 0.0011296264923849888, 0.0011130432468361663, 0.0010916715261288692, 0.0010759955706100735, 0.001068636440366523, 0.0010384942493689671, 0.0010415185494690564, 0.0010255905689419199, 0.001027959604020323, 0.0010043700632396272, 0.0009878372226924728, 0.000982343077510644, 0.0009683304870468975, 0.0009532089865464514, 0.000948823751401322, 0.0009349623759425798, 0.0009260910956489848, 0.0009143971352619732, 0.0009046689699400195, 0.0008960497146547653, 0.0008963017396631061], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 1, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#EF553B", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "PN ECAL         ΔT>10ns: 16.17%, ΔT>40ns: 6.86 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "c3cd19", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.707758737085758, 0.028835082677181656, 0.020644904337723268, 0.016232975088311164, 0.013414366120190752, 0.011453740027678756, 0.009931403727137293, 0.008756836580693794, 0.0078025624955292945, 0.007054030112532573, 0.006429273517625801, 0.005887375193242763, 0.005447146013263129, 0.005064467875001374, 0.004725324833213821, 0.004451799735415124, 0.004196086072276684, 0.003969510821618518, 0.003762841068746998, 0.0035913182617581764, 0.0034288142039159645, 0.0032744722330322945, 0.0031497564961526585, 0.003022601651654217, 0.0029026927414965214, 0.00281135113569372, 0.0027152978878565424, 0.0026259075681487856, 0.002540717272779754, 0.002461499816555311, 0.0023959056687383453, 0.0023235891023622316, 0.002256055566536167, 0.0021916393292277956, 0.002135456567393638, 0.0020817367093501774, 0.002023531272905303, 0.001980483997954862, 0.00194221975355447, 0.0018981968355565066, 0.0018493194984928218, 0.0018084852675282245, 0.0017653904002340021, 0.0017381794776768825, 0.0016944897060853902, 0.0016683496112633563, 0.0016320961433877363, 0.0016061345198548833, 0.0015624328501774457, 0.0015461919628619808, 0.0015195283522583495, 0.0014747082625020448, 0.001452363657096592, 0.0014455222576779895, 0.0014078529175748675, 0.0013865553437326095, 0.001369184138252332, 0.0013460542591744833, 0.0013211634633767159, 0.001295356514961153, 0.001284041435227082, 0.0012608044733757244, 0.001243314287035993, 0.0012243368399530872, 0.0012014092283363102, 0.001192319090674028, 0.0011682611608924383, 0.0011543047060784894, 0.0011367431312230857, 0.0011235124596518059, 0.0011070574067893238, 0.001088258430995599, 0.001074539937900558, 0.001056216885544649, 0.0010433550546376765, 0.0010312428031452638, 0.0010171316732140248, 0.001001973511719591, 0.0009879218722180787, 0.0009757382322099938, 0.0009685517882989749, 0.0009464332465264847, 0.0009400201782019165, 0.0009298473147186033, 0.0009185203368985869, 0.0009007564945821113, 0.0008917972358652286, 0.0008826000154294379, 0.0008769246284334842, 0.0008596367095548245, 0.0008480717700159002, 0.0008374705754385527, 0.0008330087932090295, 0.0008182075742929574, 0.0008062737940897258, 0.0008015621520553491, 0.00078745102212411, 0.0007831915073556585, 0.0007764452926246193, 0.000759680889527557], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 2, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#00cc96", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "EN Target       ΔT>10ns: 14.74%, ΔT>40ns: 6.21 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "4a5b82", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.7286066207933943, 0.028202212821871554, 0.019696365266082566, 0.015293383546695074, 0.012645409237945271, 0.010753078279879631, 0.009302033059006764, 0.008220285266020193, 0.007336466340787459, 0.006605875961284324, 0.005963594706338376, 0.005474438042884762, 0.0050493171895868005, 0.004691142865087931, 0.00436446667448056, 0.004103229676231929, 0.00387905236356851, 0.003658825311921186, 0.0034782568017907157, 0.003310474662843923, 0.0031419648442362704, 0.0029870730307104206, 0.002866642046838141, 0.0027498494612701596, 0.002672923325693563, 0.0025516087308045253, 0.0024709402426863645, 0.0023888683723651176, 0.00231339759611024, 0.002228570938501453, 0.0021709283139376384, 0.002099979547003818, 0.002039945974982893, 0.0020072003902442065, 0.0019468549555114846, 0.0018936303860314612, 0.001845343642821557, 0.0017969529453743874, 0.0017563588157221428, 0.0016940382504813732, 0.0016591616038787406, 0.0016276634699872422, 0.001604377720839732, 0.0015636276598315888, 0.0015161205734011774, 0.0015239171411961026, 0.0014861297759500313, 0.0014538000081604076, 0.001418663475964611, 0.0013985483310537034, 0.0013514570615723546, 0.001350729381911495, 0.0013245848912391788, 0.001296361315821549, 0.001281547837011191, 0.0012466711904085583, 0.001237939034478242, 0.0012111188412636989, 0.0011954217514365826, 0.0011719800709331738, 0.001168445626866141, 0.0011344525912802667, 0.0011347644539920637, 0.0011050855192527146, 0.0010817997701052043, 0.0010773817150214133, 0.0010372553794368643, 0.001049937796383276, 0.0010323695302853777, 0.001005185497240405, 0.0010058092226639989, 0.000979820663347581, 0.0009661506811471455, 0.0009483225294560828, 0.000941617481152447, 0.0009193712743775934, 0.0009287791328501367, 0.0009204108167502502, 0.0009020628938728593, 0.0008638597116777252, 0.0008864177811643758, 0.00085918177100077, 0.0008603252676106924, 0.0008388587176153314, 0.0008396383743948239, 0.0008333491430402508, 0.0008310621498204061, 0.0007992521532171107, 0.0007831392464409318, 0.0007965493430482034, 0.0007635438727163528, 0.0007634918955977199, 0.0007582941837344365, 0.0007465473549234156, 0.0007342807549260665, 0.0007288231574696187, 0.00071546503798098, 0.0007068368362879293, 0.0006862538973093265, 0.0006883849591732727], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 3, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#ab63fa", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "PN Target       ΔT>10ns: 12.17%, ΔT>40ns: 5.03 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "b1e9ad", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.7738691655877455, 0.023667733371810176, 0.016549061740594187, 0.012913489843261112, 0.010635959401748479, 0.009063273715829424, 0.007847288412293178, 0.006948391448675633, 0.006172350616759861, 0.00556684196765459, 0.005049209412752491, 0.004628668961933128, 0.004269328576720234, 0.003956908241805635, 0.0036881559537031822, 0.003471795721765014, 0.0032517634858904567, 0.003069219290203079, 0.0029094751189573274, 0.00276014695887754, 0.0026417428319483157, 0.0025242387059838927, 0.0024223105967169597, 0.002317454484311207, 0.0022123703716610383, 0.00214086229500438, 0.002063954212558916, 0.0019952301388867087, 0.001927334066102119, 0.0018585139923269999, 0.0018009139305797336, 0.0017488818748013699, 0.001695289817350684, 0.0016512977701912096, 0.0016024817178604015, 0.0015649576776346303, 0.0015243976341542639, 0.0014879295950605258, 0.0014547615595043918, 0.0014171895192271646, 0.001384513484198455, 0.0013450814419273057, 0.0013150334097158153, 0.0012977413911787713, 0.0012612373520464414, 0.0012372133262926858, 0.001213033300371698, 0.0011916492774480254, 0.0011645772484268103, 0.0011377212196371474, 0.0011195772001867585, 0.001092985171680104, 0.0010761611536447568, 0.0010596851359824658, 0.0010381091128529689, 0.001023277096953048, 0.0009930610645614612, 0.0009899050611782256, 0.0009594970285808146, 0.000952033020579398, 0.0009397090073680559, 0.0009213009876346587, 0.0009110049765973349, 0.000898896963617545, 0.0008833209469200551, 0.0008765049396132952, 0.0008488809100003355, 0.0008382128985642272, 0.0008298128895594176, 0.0008207288798213592, 0.0008069768650791994, 0.0007940168511860645, 0.0007801328363024005, 0.0007672088224478577, 0.0007601528148838176, 0.0007470248008105865, 0.0007424167958708052, 0.0007307407833541198, 0.0007239487760730879, 0.0007095367606234073, 0.0007005847510268531, 0.0006927847426652442, 0.0006794167283347328, 0.0006716527200117158, 0.0006678367159209594, 0.0006544447015647201, 0.0006440046903730281, 0.0006436206899613796, 0.0006356406814068105, 0.0006268086719388963, 0.0006179046623937981, 0.0006112926553057265, 0.0006056766492853681, 0.0005970606400490062, 0.000588168630516772, 0.000578748620418521, 0.0005707806118768159, 0.000568236609149645, 0.0005633406039011274, 0.0005590325992829464], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 4, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#FFA15A", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "Inclusive         ΔT>10ns: 1.53%, ΔT>40ns: 0.60 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "0b95b4", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.9732535556916273, 0.002184900018063149, 0.0017018121163955885, 0.0014253230656280061, 0.0012203075903480462, 0.0010699502414711155, 0.0009484736639033173, 0.0008458075887013356, 0.0007638203990967919, 0.0006910484556007543, 0.0006305001718912573, 0.0005899340885063903, 0.0005476896269643041, 0.0005036401172153971, 0.0004819795387335478, 0.0004416351279354834, 0.00042143125502405087, 0.00040021402171580664, 0.0003814985218871912, 0.00037009821742306, 0.00034656925570958916, 0.00032117191076449684, 0.00031151331948238567, 0.00029909965462144276, 0.00029358950746377936, 0.0002774390761395934, 0.00027952913195801747, 0.0002620486651130163, 0.00024630991145003513, 0.00024352317035880304, 0.00024102143687917426, 0.00022851276948103027, 0.00022173592182735228, 0.00021397738128926295, 0.0002066305184123784, 0.00019944199309749567, 0.00019915698548589238, 0.0001931718256422235, 0.00019063842465019432, 0.00017774974710324596, 0.00017812975725205033, 0.00017480466845001206, 0.0001651460771679009, 0.00015973093254743857, 0.00015938258991103456, 0.000155424150860989, 0.00015162404937294526, 0.00014842563062050844, 0.00014769727783530006, 0.00014126877281802605, 0.0001383870291895929, 0.00013522027794955643, 0.00013965372968560748, 0.00013129350641191123, 0.00012648004452705584, 0.00012996347089109592, 0.00012724006482466458, 0.00011713812836894831, 0.00012052655219578731, 0.00011419304971571441, 0.00011254633907089546, 0.00011365470200490822, 0.00011010794061606738, 0.00011127963857488087, 0.00010570615639241672, 0.0001009876970447624, 0.0001009876970447624, 9.91193138131409e-05, 9.402084431668221e-05, 9.772594326752487e-05, 9.734593311872049e-05, 9.335582655627456e-05, 9.237413367186326e-05, 9.08224255642454e-05, 8.718066163820348e-05, 8.417224796016886e-05, 8.458392562137359e-05, 8.132217184413605e-05, 8.388724034856557e-05, 8.186051955494225e-05, 8.344389517496048e-05, 7.850376324050361e-05, 7.514700692606498e-05, 7.676205005848356e-05, 7.555868458726971e-05, 7.156857802482378e-05, 6.814848668558441e-05, 7.141024046282196e-05, 7.153691051242342e-05, 6.922518210719681e-05, 6.894017449559353e-05, 7.046021509081102e-05, 6.732513136317495e-05, 6.447505524714213e-05, 6.400004256113666e-05, 6.542508061915307e-05, 6.086495883350058e-05, 6.12133014699046e-05, 6.038994614749511e-05, 6.438005270994104e-05], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 5, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#19d3f3", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "GMM ECAL      ΔT>10ns: 0.85%, ΔT>40ns: 0.33 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "8ad4b6", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.98379829654088, 0.001607822012639489, 0.001193222657148705, 0.0009739038821287527, 0.0008105948937480067, 0.0006906419229286295, 0.0005954808194247652, 0.000526380926842968, 0.0004623187416996828, 0.0004262217828882962, 0.0003793750704089032, 0.0003463324696507877, 0.00032221494112625687, 0.00029547939141320786, 0.0002735435472124421, 0.00026683982629032744, 0.0002491483497739775, 0.00023542357202810963, 0.00021547107721258492, 0.0002019446344052411, 0.00019349556602411434, 0.00018635550823724667, 0.0001759627574585837, 0.00016545100571680626, 0.00016477666692582432, 0.00015446325012257099, 0.0001490685397947154, 0.00013942946178244404, 0.0001424838198357152, 0.00013431242036852217, 0.00013351908061442577, 0.00012510967922100383, 0.00012284866092182907, 0.00011658127686446743, 0.00011451859350381676, 0.00011666061083987706, 0.00010412584272515379, 0.00010599019114728036, 0.00010194415840138866, 9.73824548153343e-05, 9.40900948358342e-05, 9.25430823153462e-05, 9.619244518418969e-05, 9.09960697948582e-05, 8.782271077847256e-05, 8.504602163913513e-05, 8.084132094242416e-05, 8.353867610635196e-05, 7.750929397521924e-05, 7.608128241784571e-05, 7.623995036866499e-05, 7.493093977440591e-05, 7.429626797112877e-05, 7.005190028671299e-05, 6.703720922114664e-05, 6.759254704901412e-05, 6.67992072949177e-05, 6.564886465147792e-05, 6.255483961050192e-05, 6.449852200803812e-05, 6.33085123768935e-05, 6.00161523973934e-05, 6.247550563509227e-05, 6.041282227444161e-05, 5.815180397526684e-05, 5.331243147527874e-05, 5.5970119651501714e-05, 5.3907436290851046e-05, 5.422477219248961e-05, 5.466110905724264e-05, 5.0297740409712385e-05, 5.01390724588931e-05, 5.1408416065447354e-05, 4.8076389098242435e-05, 4.867139391381474e-05, 4.934573270479669e-05, 4.526003297120018e-05, 4.54980348974291e-05, 4.224534190563382e-05, 4.577570381136285e-05, 4.256267780727239e-05, 4.351468551218808e-05, 4.260234479497721e-05, 4.3990689364645924e-05, 4.069832938514582e-05, 4.276101274579649e-05, 4.1531336126947056e-05, 4.2721345758091666e-05, 3.792164024580839e-05, 4.284034672120613e-05, 3.732663543023608e-05, 4.113466624989885e-05, 3.728696844253126e-05, 3.411360942614562e-05, 3.300293377041065e-05, 3.2804598831886543e-05, 3.5779622909748086e-05, 3.625562676220593e-05, 3.1812924139266034e-05, 3.673163061466378e-05], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 6, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#FF6692", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "GMM Target    ΔT>10ns: 0.75%, ΔT>40ns: 0.29 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "0fd6f3", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.9853946087235423, 0.0015096363433802565, 0.001114252422553961, 0.000894474417074124, 0.0007422659707709825, 0.0006291920812306835, 0.0005455523742769058, 0.00047606771976926263, 0.0004212793403012828, 0.0003850174265348615, 0.0003538346786461708, 0.0003139507367538268, 0.0002872226671349491, 0.0002644913182067447, 0.0002528342161922809, 0.00023276734772452535, 0.00021865392778558525, 0.00020474867038261773, 0.00019088504548684472, 0.00018151773136807915, 0.0001742736751162338, 0.00016265820560896452, 0.0001622835130442139, 0.00015566394440028622, 0.0001451309200800743, 0.00013751217126347831, 0.0001291440373173811, 0.00012573017172743097, 0.00012352364884612175, 0.00012077590337128386, 0.0001141147022201617, 0.00011336531709066045, 0.00010533024320211933, 0.0001046224905798126, 0.00010212454014814178, 9.629598914090988e-05, 9.338171363729393e-05, 9.17580458567079e-05, 9.071723317684507e-05, 8.334827940341616e-05, 8.563806729911441e-05, 8.06837989429673e-05, 8.318174937463811e-05, 7.972625127749348e-05, 7.564626557243115e-05, 7.473035041415185e-05, 7.402259779184512e-05, 7.139974983859077e-05, 7.069199721628404e-05, 7.135811733139626e-05, 6.752792666950101e-05, 6.665364401841622e-05, 6.448875364430152e-05, 6.032550292485016e-05, 6.4447121137107e-05, 6.0949990532767864e-05, 5.424715687445118e-05, 5.8202245057929965e-05, 5.4038994338478613e-05, 5.603735468381526e-05, 5.599572217662075e-05, 5.5121439525535965e-05, 4.925125601110955e-05, 4.8501870881608305e-05, 5.220716402192001e-05, 4.979247860463823e-05, 4.945941854708212e-05, 4.6586775550660684e-05, 4.6045552957132005e-05, 4.887656344635893e-05, 4.2673319874376406e-05, 4.4588415205324035e-05, 4.421372264057341e-05, 4.329780748229411e-05, 4.2381892324014815e-05, 4.23402598168203e-05, 4.1341079644151974e-05, 4.088312206501232e-05, 4.192393474487516e-05, 3.892639422687019e-05, 3.759415399664575e-05, 3.6095383737643264e-05, 3.813537659017443e-05, 3.380559584194502e-05, 3.763578650384027e-05, 3.676150385275548e-05, 3.513783607216945e-05, 3.4388450942668206e-05, 3.239009059733156e-05, 3.401375837791759e-05, 3.230682558294253e-05, 3.1890500510997395e-05, 3.1057850367107125e-05, 3.326437324841634e-05, 3.0808055323940044e-05, 2.8101942356296658e-05, 2.8310104892269226e-05, 2.543746189584779e-05, 2.985050765846623e-05, 2.9600712615299148e-05], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 7, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#B6E880", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "Signal 5MeV    ΔT>10ns: 0.73%, ΔT>40ns: 0.27 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "e11a84", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.9860579738840874, 0.0013448408905650181, 0.0010272049237738446, 0.0008495582426672169, 0.0007060530330857691, 0.0006099202717994221, 0.0005298867409883633, 0.0004598458359895522, 0.00041182571750291683, 0.00037777677029081315, 0.000332809954135698, 0.0003081059625443076, 0.00029219178069517216, 0.0002666550702860944, 0.000249723120993119, 0.00021900504905176458, 0.0002245565078363467, 0.00020253572132417096, 0.00019439358177345052, 0.0001776466811066278, 0.00017847939992431512, 0.00015858667261289586, 0.00015858667261289586, 0.00014970433855756446, 0.00014581831740835698, 0.00012842374654999968, 0.00012592559009693772, 0.00012731345479308326, 0.00011917131524236281, 0.0001168582074154536, 0.00011630306153699539, 0.000109178689430115, 0.00010557024122013662, 9.82608204871035e-05, 0.00010483004671552568, 9.668790716480523e-05, 9.85383934263326e-05, 8.715790291793926e-05, 9.039625387561216e-05, 9.095139975407037e-05, 8.567751390871737e-05, 7.235401282572028e-05, 7.568488809646955e-05, 7.99410064979825e-05, 7.281663439110213e-05, 7.79054716103024e-05, 7.041100225111654e-05, 6.467449484038168e-05, 6.624740816267994e-05, 6.328663014423615e-05, 5.986323056041051e-05, 6.004827918656325e-05, 5.958565762118141e-05, 6.060342506502146e-05, 5.745759842042493e-05, 5.9770706247334146e-05, 5.403419883659929e-05, 5.699497685504309e-05, 5.597720941120303e-05, 5.079584787892639e-05, 4.802011848663532e-05, 5.144351807046096e-05, 4.940798318278085e-05, 4.598458359895522e-05, 4.357895145896963e-05, 4.2653708328205944e-05, 4.089574637975494e-05, 4.450419458973332e-05, 4.5429437720497e-05, 4.163594088436589e-05, 4.135836794513678e-05, 4.478176752896242e-05, 3.793496836131114e-05, 3.9230308744380304e-05, 4.3671475772045996e-05, 4.163594088436589e-05, 3.821254130054025e-05, 3.8490114239769356e-05, 3.7287298169776566e-05, 3.59919577867074e-05, 3.608448209978377e-05, 3.3031179768263605e-05, 3.071807194135439e-05, 2.9052634305979753e-05, 3.4233995838256396e-05, 3.0070401749819808e-05, 3.57143848474783e-05, 3.0162926062896177e-05, 3.45115687774855e-05, 3.071807194135439e-05, 2.581428334830685e-05, 2.6184380600612325e-05, 3.118069350673623e-05, 2.7849818235986962e-05, 2.581428334830685e-05, 2.9145158619056122e-05, 2.6276904913688694e-05, 2.5351661782925008e-05, 2.590680766138322e-05, 2.6647002165994168e-05], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}, {"connectgaps": false, "error_x": {"visible": false}, "error_y": {"visible": false}, "fill": "none", "hoverinfo": "all", "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hoveron": "points", "hovertemplate": "", "hovertext": "", "index": 8, "legend": "legend", "legendgroup": "", "legendgrouptitle": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "text": ""}, "legendrank": 1000, "line": {"backoff": "auto", "color": "#FF97FF", "dash": "solid", "shape": "hvh", "simplify": true, "width": 2}, "mode": "lines", "name": "Signal 500MeV ΔT>10ns: 0.50%, ΔT>40ns: 0.18 %", "opacity": 1, "showlegend": true, "stackgroup": "", "text": "", "type": "scatter", "uid": "df1f1f", "visible": true, "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "xaxis": "x", "xcalendar": "gregorian", "xhoverformat": "", "xperiod": 0, "y": [0.9899527070932623, 0.0010890781067893678, 0.00080566975930348, 0.0006387788361486352, 0.0005374193947463349, 0.0004357537314666864, 0.0003826242357467496, 0.0003403656166726788, 0.00029565722257982133, 0.00026794414267979663, 0.0002526330488123797, 0.0002315037392753443, 0.00019674755619630783, 0.00018235512796093589, 0.00017607757947529494, 0.0001678095887868898, 0.0001541827152448887, 0.00013274718383050497, 0.0001319816291371341, 0.00012233564000066144, 0.00012049830873657141, 0.00010518721486915446, 0.00010579965862385113, 9.753166793544598e-05, 0.00010702454613324449, 0.0001019718851569969, 8.28330178227257e-05, 9.29383397752209e-05, 8.972301006306333e-05, 8.880434443101832e-05, 8.176124125200652e-05, 7.058414272879215e-05, 7.716791309178144e-05, 6.997169897409547e-05, 7.272769587023052e-05, 6.430659424315119e-05, 6.614392550724123e-05, 5.848837857353275e-05, 5.2210830087891805e-05, 5.57323816773977e-05, 5.680415824811689e-05, 5.665104730944272e-05, 5.466060510667852e-05, 4.807683474368923e-05, 4.7617501927666715e-05, 4.639261441827336e-05, 4.1033731564677426e-05, 4.225861907407078e-05, 4.195239719672245e-05, 4.6239503479599194e-05, 4.3330395644789974e-05, 4.195239719672245e-05, 4.225861907407078e-05, 4.072750968732909e-05, 3.91964003005874e-05, 4.378972846081248e-05, 3.353129556964312e-05, 3.904328936191323e-05, 4.11868425033516e-05, 3.06221877348339e-05, 3.444996120168814e-05, 3.3684406508317294e-05, 3.261262993759811e-05, 3.0775298673508075e-05, 3.2765740876272274e-05, 3.353129556964312e-05, 3.0315965857485564e-05, 2.847863459339553e-05, 3.2153297121575595e-05, 2.5722637697260478e-05, 2.7559968961350512e-05, 2.5722637697260478e-05, 2.7253747084002173e-05, 2.771307990002468e-05, 2.587574863593465e-05, 2.204797516908041e-05, 2.9397300225440547e-05, 2.4344639249192953e-05, 2.4344639249192953e-05, 2.204797516908041e-05, 2.3119751739799597e-05, 2.250730798510292e-05, 1.883264545692285e-05, 2.526330488123797e-05, 2.4038417371844614e-05, 2.0976198598361223e-05, 1.4698650112720273e-05, 1.9904422027642035e-05, 2.0210643904990374e-05, 2.0363754843664545e-05, 2.0057532966316206e-05, 1.8067090763552004e-05, 1.6689092315484476e-05, 2.174175329173207e-05, 1.898575639559702e-05, 1.4545539174046104e-05, 2.0516865782338713e-05, 1.852642357957451e-05, 1.883264545692285e-05, 1.822020170222617e-05], "yaxis": "y", "ycalendar": "gregorian", "yhoverformat": "", "yperiod": 0}], "layout": {"activeselection": {"fillcolor": "rgba(0,0,0,0)", "opacity": 0.5}, "activeshape": {"fillcolor": "rgb(255,0,255)", "opacity": 0.5}, "annotations": [], "autosize": false, "autotypenumbers": "strict", "calendar": "gregorian", "clickmode": "event", "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "dragmode": "zoom", "font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 12}, "height": 600, "hidesources": false, "hoverdistance": 20, "hoverlabel": {"align": "left", "font": {"family": "<PERSON>l, sans-serif", "size": 13}, "grouptitlefont": {"family": "<PERSON>l, sans-serif", "size": 13}, "namelength": 15}, "hovermode": "closest", "images": [], "legend": {"bgcolor": "rgba(0, 0, 0, 0)", "bordercolor": "#444", "borderwidth": 0, "entrywidthmode": "pixels", "font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 18}, "groupclick": "togglegroup", "grouptitlefont": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 13}, "itemclick": "toggle", "itemdoubleclick": "toggleothers", "itemsizing": "trace", "itemwidth": 30, "orientation": "v", "title": {"text": ""}, "traceorder": "normal", "valign": "middle", "visible": true, "x": 1, "xanchor": "right", "xref": "paper", "y": 1, "yanchor": "top", "yref": "paper"}, "margin": {"autoexpand": true, "b": 20, "l": 90, "pad": 0, "r": 20, "t": 80}, "minreducedheight": 64, "minreducedwidth": 64, "modebar": {"activecolor": "rgba(255, 255, 255, 0.7)", "add": "", "bgcolor": "rgba(0, 0, 0, 0.5)", "color": "rgba(255, 255, 255, 0.3)", "orientation": "h", "remove": ""}, "newselection": {"line": {"dash": "dot", "width": 1}, "mode": "immediate"}, "newshape": {"drawdirection": "diagonal", "fillcolor": "rgba(0,0,0,0)", "fillrule": "evenodd", "label": {"text": "", "texttemplate": ""}, "layer": "above", "legend": "legend", "legendgroup": "", "legendgrouptitle": {"text": ""}, "legendrank": 1000, "line": {"color": "#444", "dash": "solid", "width": 4}, "opacity": 1, "showlegend": false, "visible": true}, "paper_bgcolor": "rgba(0, 0, 0, 0)", "plot_bgcolor": "rgba(0, 0, 0, 0)", "scattermode": "overlay", "selections": [], "separators": ".,", "shapes": [], "showlegend": true, "sliders": [], "spikedistance": -1, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"automargin": false, "font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 20}, "pad": {"b": 0, "l": 0, "r": 0, "t": 0}, "text": "", "x": 0.5, "xanchor": "auto", "xref": "container", "y": "auto", "yanchor": "auto", "yref": "container"}, "uniformtext": {"mode": false}, "updatemenus": [], "width": 800, "xaxis": {"anchor": "y", "automargin": true, "autorange": true, "autotickangles": [0, 30, 90], "autotypenumbers": "strict", "color": "#444", "constrain": "range", "constraintoward": "center", "domain": [0, 1], "dtick": 20, "exponentformat": "B", "fixedrange": false, "hoverformat": "", "layer": "above traces", "linecolor": "#666666", "linewidth": 2, "minexponent": 3, "mirror": true, "nticks": 6, "range": [0, 99], "rangemode": "normal", "separatethousands": false, "showexponent": "all", "showgrid": false, "showline": true, "showspikes": false, "showticklabels": true, "side": "bottom", "tick0": 0, "tickangle": "auto", "tickfont": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 16}, "tickformat": "", "ticklabeloverflow": "hide past div", "ticklabelposition": "outside", "ticklabelstep": 1, "tickmode": "auto", "tickprefix": "", "ticks": "", "ticksuffix": "", "title": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 20}, "standoff": 15, "text": "ECAL Hit ΔT"}, "type": "linear", "visible": true, "zeroline": true, "zerolinecolor": "rgba(0, 0, 0, 0)", "zerolinewidth": 2}, "yaxis": {"anchor": "x", "automargin": true, "autorange": true, "autotypenumbers": "strict", "color": "#444", "constrain": "range", "constraintoward": "middle", "domain": [0, 1], "dtick": "D2", "exponentformat": "power", "fixedrange": false, "hoverformat": "", "layer": "above traces", "linecolor": "#666666", "linewidth": 2, "minexponent": 3, "mirror": true, "nticks": 0, "range": [-5.105763765872864, 0.26410803777060576], "separatethousands": false, "showexponent": "all", "showgrid": false, "showline": true, "showspikes": false, "showticklabels": true, "side": "left", "tick0": 0, "tickangle": "auto", "tickfont": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 16}, "tickformat": "", "ticklabeloverflow": "hide past div", "ticklabelposition": "outside", "tickmode": "auto", "tickprefix": "", "ticks": "", "ticksuffix": "", "title": {"font": {"color": "#2a3f5f", "family": "\"Open Sans\", verdana, arial, sans-serif", "size": 20}, "standoff": 15, "text": "ECAL Cell Number (Normalized)"}, "type": "log", "visible": true, "zeroline": true, "zerolinecolor": "rgba(0, 0, 0, 0)", "zerolinewidth": 2}, "computed": {"margin": {"b": 65, "l": 92, "r": 20, "t": 80}}}, "frames": [], "config": {"autosizable": false, "displayModeBar": false, "displaylogo": true, "doubleClick": false, "doubleClickDelay": 300, "editSelection": true, "editable": false, "edits": {}, "fillFrame": false, "frameMargins": 0, "globalTransforms": [], "linkText": "Edit chart", "locale": "en-US", "locales": {}, "logging": 1, "mapboxAccessToken": null, "modeBarButtons": false, "modeBarButtonsToAdd": [], "modeBarButtonsToRemove": [], "notifyOnLogging": 0, "plotGlPixelRatio": 2, "plotlyServerURL": "", "queueLength": 0, "responsive": false, "scrollZoom": false, "sendData": true, "setBackground": "_function", "showAxisDragHandles": true, "showAxisRangeEntryBoxes": true, "showEditInChartStudio": false, "showLink": false, "showSendToCloud": false, "showSources": false, "showTips": false, "staticPlot": true, "toImageButtonOptions": {}, "topojsonURL": "https://cdn.plot.ly/", "typesetMath": true, "watermark": false}, "version": "2.29.1"}