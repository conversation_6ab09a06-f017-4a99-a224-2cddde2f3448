:root {
    --slidev-theme-primary: black;
    --slidev-theme-secondary: rgba(0, 0, 0, 0.6);
    --slidev-code-font-size: 3.3vw;
    --slidev-code-line-height: 1.2em;
    counter-reset: h2counter;
}

.slidev-layout {
    @apply h-full text-$slidev-theme-primary;
    font-size: 6vw;

    * {
        text-justify: inter-character;
    }

    .abs {
        position: absolute;
    }

    p+h2,
    ul+h2,
    table+h2 {
        @apply mt-10;
    }

    h1 {
        /*this is title*/
        @apply text-4xl text-left;
    }

    h2 {
        color: cadetblue;
        @apply text-6xl text-left ml-4 mb-4 mt-6;
    }

    h3 {
        color: rgb(86, 134, 136);
        @apply text-4xl text-left ml-14 mb-4;
    }

    h4 {
        @apply text-3xl text-left ml-28 mb-2;
    }

    h5 {
        @apply text-base;
    }

    h6 {
        @apply text-sm pt-1 uppercase tracking-widest font-500 -ml-[0.05em];
    }

    h6:not(.opacity-100) {
        @apply opacity-40;
    }

    hr {
        content: ' ';
        display: block;
        width: 98%;
        position: absolute;
        top: 11vh;
        left: 1vw;
        border-style: none;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-primary) 0%, white 50%, white 100%) 0.5;
    }
}

@keyframes background-anime {
    0% {
        background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f046, rgb(86, 134, 136, 0.9));
    }

    50% {
        background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f066, rgb(86, 134, 136, 0.9));
    }

    100% {
        background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f086, rgb(86, 134, 136, 0.9));
    }
}

.slidev-layout.cover {
    @apply h-full grid;

    h1 {
        @apply text-6xl text-left h-80 c-white ml-2;
    }

    h1:after {
        border-bottom: none;
    }
}

.slidev-layout.intro {
    @apply h-full grid;

    h1 {
        color: inherit;
        @apply text-6xl leading-20;
    }
}


.slidev-layout.statement {
    @apply text-center grid h-full;
}

.slidev-layout.quote {
    @apply grid h-full;

    h1+p {
        @apply mt-2;
    }
}

.slidev-layout.section {
    h1 {
        @apply text-5xl font-500 leading-20 ml-10 mb-1;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-primary), white) 1;
    }

    h1:after {
        content: unset;
    }

    h2 {
        @apply text-2xl font-300 ml-10;
    }

    h2:before {
        content: unset;
    }
}

.slidev-layout.center {

    h1,
    h2,
    h3,
    p {
        @apply text-center;
    }

    h1:after,
    h2:before,
    h3:before {
        content: unset;
    }

    h1 {
        @apply text-6xl font-500 leading-20 ml-10 mb-6 text-center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to left, white 0%, var(--slidev-theme-primary) 50%, white 100%)1;
    }
}

.slidev-layout.image-left {
    h1 {
        position: relative;
        left: -108%;
    }
}
