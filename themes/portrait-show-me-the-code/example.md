---
layout: cover
title: Presentation title
abstract: 这是摘要部分
img: https://images.jieyu.ai/images/hot/book-cover.jpg
brand: 💤illionare量化课程
presenter: <PERSON>【量化风云】
---


---
layout: prelude-brand-only
---

<Brand/>

---
layout: fact
---

# Slide Title


Slide Subtitle

* Slide bullet text
  * Slide bullet text
  * Slide bullet text
* Slide bullet text
* Slide bullet text

---
layout: image-right
image: https://source.unsplash.com/collection/94734566/1920x1080
---

# Slide Title

Colons can be used to align columns.

| Tables        |      Are      |  Cool |
| ------------- | :-----------: | ----: |
| col 3 is      | right-aligned | $1600 |
| col 2 is      |   centered    |   $12 |
| zebra stripes |   are neat    |    $1 |

---
layout: section
---

# Section Title

---
layout: statement
---

# Statement

---
layout: fact
---

# 100%
Fact information

---
layout: quote
---

# "Notable quote"
Attribution

---
layout: image-left
image: https://source.unsplash.com/collection/94734566/1920x1080
---

# Code

```ts {all|2|1-6|all}
interface User {
  id: number
  firstName: string
  lastName: string
  role: string
}

function updateUser(id: number, update: Partial<User>) {
  const user = getUser(id)
  const newUser = {...user, ...update}  
  saveUser(id, newUser)
}
```

---
layout: iframe
url: http://***************:5180/aaron_24/lab/workspaces/auto-D/tree/examples/%E6%95%B0%E6%8D%AE%E8%8E%B7%E5%8F%96/%E5%90%8C%E8%8A%B1%E9%A1%BA%E6%A6%82%E5%BF%B5%E6%9D%BF%E5%9D%97%E6%95%B0%E6%8D%AE.ipynb
scale: 1
---
# show me code

---
layout: center
class: "text-center"
---

# Learn More

[Documentations](https://sli.dev) / [GitHub Repo](https://github.com/slidevjs/slidev)
