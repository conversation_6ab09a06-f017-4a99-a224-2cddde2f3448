---
aspectRatio: "3/4"
canvasWidth: 900
layout: paid-article
title: "可运行的研报"
subtitle: "Notebook格式，自带数据、依赖库，提供服务器，每一个示例都可运行。"
price: "¥299"
description: "下单后，扫描客服二维码，获取登录地址和账号"
qrcode: "https://images.jieyu.ai/images/hot/quantfans.png"
bgImg: "https://images.unsplash.com/photo-1636955840493-f43a02bfa064"
imageSource: "图片来源：Unsplash"
---

# 自定义背景图片示例

这个示例展示了如何使用 frontmatter 中的 `bgImg` 字段来自定义海报的背景图片，以及如何使用 `subtitle` 字段添加副标题。

## 🎨 新的背景图片显示方式

现在背景图片采用了全新的显示方式：
- **等宽显示** - 背景图片宽度固定为900px，与画面等宽
- **不缩放** - 保持图片原始比例，不进行拉伸变形
- **可截断高度** - 如果图片高度超出画面，会从底部开始显示，顶部可以被截断
- **从下往上渐变透明** - 背景图片在底部完全显示，向上逐渐变透明
- **上部留白** - 为标题和主要文字提供清晰的显示区域
- **文字优化** - 上部文字改为深色，确保在白色遮罩上清晰可读
- **底部保留** - 装饰性文字和二维码区域保持原有的白色文字效果

## 配置说明

在 frontmatter 中添加相关字段：

```yaml
---
layout: paid-article
title: "您的商品标题"
subtitle: "副标题描述（可选）"
price: "¥299"
description: "商品描述文字"
qrcode: "https://images.jieyu.ai/images/hot/quantfans.png"
bgImg: "您的背景图片URL"
imageSource: "图片来源说明（可选）"
---
```

### 新增字段说明

- **`subtitle`**: 副标题，显示在主标题下方，用于补充说明
- **`bgImg`**: 背景图片URL，替代之前的 `img` 字段
- **`imageSource`**: 图片来源说明，显示在海报底部中央的小字

## 背景图片要求

1. **宽度要求**: 建议图片宽度至少为900px，以确保清晰显示
2. **高度建议**: 图片高度可以任意，系统会自动从底部开始显示
3. **格式支持**: JPG、PNG、WebP 等常见格式
4. **内容考虑**:
   - 重要内容应放在图片下半部分，因为上半部分可能被截断
   - 避免在图片上部放置重要的视觉元素
5. **色彩搭配**:
   - 图片底部区域考虑与白色文字的对比度
   - 图片上部区域会被白色遮罩覆盖，不影响文字显示

## 默认行为

- 如果不设置 `bgImg` 字段，将使用默认背景图片
- 如果不设置 `subtitle` 字段，将不显示副标题
- 如果不设置 `imageSource` 字段，将不显示图片来源说明
- 背景图片使用 contain 模式，居上显示
- 从上到下渐变透明，为下方文字提供清晰背景

## 设计建议

1. **选择合适的图片**: 避免过于复杂或对比度过强的图片
2. **考虑文字可读性**: 确保白色文字在背景上清晰可见
3. **保持品牌一致性**: 选择符合品牌调性的图片
4. **优化加载速度**: 压缩图片大小，提升加载体验

## 示例背景图片

- 抽象几何图案
- 渐变色彩背景
- 产品相关场景图
- 品牌元素图片

通过自定义背景图片，您可以让海报更好地匹配您的品牌形象和产品特色。
