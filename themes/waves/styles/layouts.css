:root {
    --slidev-theme-primary: #146a8cf4;
    --slidev-theme-secondary: #146a8c80;
    counter-reset: h2counter;
}

.slidev-layout {
    @apply h-full text-$slidev-theme-primary;

    padding: 3vw;

    p+h2,
    ul+h2,
    table+h2 {
        @apply mt-10;
    }

    h1 {
        /*this is title*/
        @apply text-4xl text-left mb-16;
    }

    h2 {
        @apply text-3xl text-left ml-4 mb-4;
    }

    h3 {
        @apply text-2xl text-left ml-14 mb-4;
    }

    h4 {
        @apply text-xl text-left ml-28 mb-2;
    }

    h5 {
        @apply text-base;
    }

    h6 {
        @apply text-sm pt-1 uppercase tracking-widest font-500 -ml-[0.05em];
    }

    h6:not(.opacity-100) {
        @apply opacity-40;
    }

    hr {
        content: ' ';
        display: block;
        width: 98%;
        position: absolute;
        top: 11vh;
        left: 1vw;
        border-style: none;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-primary) 0%, white 50%, white 100%) 0.5;
    }
}

.slidev-layout.cover {
    @apply h-full grid;

    h1 {
        @apply text-6xl text-left h-80 c-white ml-2;
    }

    h1:after {
        border-bottom: none;
    }
}

.slidev-layout.intro {
    @apply h-full grid;

    h1 {
        color: inherit;
        @apply text-6xl leading-20;
    }
}


.slidev-layout.statement {
    @apply text-center grid h-full;
}

.slidev-layout.quote {
    @apply grid h-full;

    h1+p {
        @apply mt-2;
    }
}

.slidev-layout.section {
    h1 {
        @apply text-5xl font-500 leading-20 ml-10 mb-1;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-primary), white) 1;
    }

    h1:after {
        content: unset;
    }

    h2 {
        @apply text-2xl font-300 ml-10;
    }

    h2:before {
        content: unset;
    }
}

.slidev-layout.center {

    h1,
    h2,
    h3,
    p {
        @apply text-center;
    }

    h1:after,
    h2:before,
    h3:before {
        content: unset;
    }

    h1 {
        @apply text-6xl font-500 leading-20 ml-10 mb-6 text-center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to left, white 0%, var(--slidev-theme-primary) 50%, white 100%)1;
    }
}

.slidev-layout.image-left {
    h1 {
        position: relative;
        left: -108%;
    }
}
