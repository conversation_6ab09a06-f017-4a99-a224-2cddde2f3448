:root {
    --slidev-theme-primary: #404040;
    --slidev-theme-secondary: #F8D254;
    counter-reset: h2counter;
    font-family: 'WenQuanYi Micro Hei';
    --slidev-background-color: #fcfcfc;
}

.slidev-layout {
    @apply h-full text-left text-$slidev-theme-primary;

    p+h2,
    ul+h2,
    table+h2 {
        @apply mt-10;
    }

    h1 {
        font-size: 5.5vw;
        color: var(--slidev-theme-secondary);
        margin: 6vw 0;
    }


    h2 {
        text-align: left;
        margin: 4vw 1vw;
        font-size: 4vw;
        color: var(--slidev-theme-secondary);
        border-left: .5vw solid color-mix(in srgb, var(--slidev-theme-secondary), #000 10%);
        padding: 0.2em 0.2em 0.2em 0.5em;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .05), 0 2px 5px 0 rgba(0, 0, 0, .03), 0 3px 1px -2px rgba(0, 0, 0, .01);
    }

    h3 {
        text-align: left;
        font-size: 3.5vw;
        margin: 3vw 1vw;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 20%);
        border-left: .3vw solid color-mix(in srgb, var(--slidev-theme-secondary), #000 15%);
        padding: 0.2em 0.2em 0.2em 0.5em;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .05), 0 2px 5px 0 rgba(0, 0, 0, .03), 0 3px 1px -2px rgba(0, 0, 0, .01);

    }

    h4 {
        text-align: left;
        font-size: 3.2vw;
        border-left: 2px solid color-mix(in srgb, var(--slidev-theme-secondary), #000 40%);
        padding: 0.2em 0.2em 0.2em 0.5em;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 30%);
        margin: 2vw 1vw;
    }


    p {
        line-height: 4vw;
        font-size: 2.8vw;
        text-align: left;
        font-weight: 100;
        margin: 2vw 1vw;
    }

    .admonition p {
        font-size: 2vw !important;
    }

    .admonition li {
        font-size: 2vw !important;
    }
}

.slidev-layout.cover {
    @apply h-full grid;

    h1 {
        @apply text-6xl text-left h-80 ml-2;
        color: white;
    }

}

.slidev-layout.intro {
    @apply h-full grid;

    h1 {
        color: inherit;
        @apply text-6xl leading-20;
    }
}


.slidev-layout.statement {
    @apply text-left h-full;
}

.slidev-layout.quote {
    @apply h-full;

    h1+p {
        @apply mt-2;
    }
}

.slidev-layout.section {
    h1 {
        @apply text-5xl font-500 leading-20 ml-10 mb-1;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-secondary), white) 1;
    }

    h1:after {
        content: unset;
    }

    h2 {
        @apply text-2xl font-300 ml-10;
    }

    h2:before {
        content: unset;
    }
}

.slidev-layout.center {

    h1,
    h2,
    h3,
    p {
        @apply text-center;
    }

    h1:after,
    h2:before,
    h3:before {
        content: unset;
    }

    h1 {
        @apply text-6xl font-500 leading-20 ml-10 mb-6 text-center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to left, white 0%, var(--slidev-theme-primary) 50%, white 100%)1;
    }
}
