<script setup lang="ts">
import { computed, onMounted } from 'vue';


const props = defineProps({
    date: {
        type: String,
        required: true
    },
    motto: {
        type: String,
        required: false
    },
    img: {
        type: String,
        default: "http://"
    }
})

const calendar = computed(() => {
    var dt = new Date(props.date)

    return {
        "day": dt.getDate().toString().padStart(2, "0"),
        "month_ch": (dt.getMonth() + 1) + "月",
        "month_en": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"][dt.getMonth()],
        "week_day": ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][dt.getDay()]
    }
})

const titleImg = computed(() => {
    let width = $slidev.configs.stamp_width ? `${$slidev.configs.stamp_width}` : '60%'
    let height = $slidev.configs.stamp_height ? `${$slidev.configs.stamp_height}` : '60%'
    return {
        "background-image": "url(" + props.img + ")",
        // used for stamp background
        "--stamp-bg-width": width,
        "--stamp-bg-height": height
    }
})

// 获取 tags 配置
const tags = computed(() => {
    if ($slidev.configs.tags && Array.isArray($slidev.configs.tags)) {
        return $slidev.configs.tags
    }
    return []
})

// onMounted(() => {
//     // console.log($slidev.nav.currentRoute)
//     var el = document.getElementById("title-img")
//     if (el == null) {
//         var html = `<img id="title-img" src=${props.img} class='title-img'>`;
//         document.getElementById("slideshow").insertAdjacentHTML("afterbegin", html)
//     }

//     setInterval(function () {
//         // console.log($slidev.nav.currentPage)
//         var el = document.getElementById("title-img")

//         // 在其它页面时隐藏
//         if ($slidev.nav.currentPage == 1) {
//             el.style.display = ""
//         } else {
//             el.style.display = "none"
//         }
//     });

// })

</script>


<style>
.cover {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: url('https://images.jieyu.ai/images/hot/instructor/flora-3.jpg');
    background-size: cover;

    .title {
        position: absolute;
        left: 0;
        font-size: 12vw; /* 调整字体大小 */
        width: 100%;
        text-align: center;
        text-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
        color: white;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 8px 0; /* 增大内边距 */
    }

    .motto {
        @apply relative left--10 mt-30px ml-20px text-2xl text-white rounded-full flex items-center pl-40px pr-40px pt-15px pb-15px color-black; /* 增大尺寸 */
        background-image: url("https://images.jieyu.ai/images/hot/blue-purple-gradient.jpg");
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
        font-weight: 500;
        width: fit-content;
        font-size: 4vw; /* 明确设置更大的字体 */
    }

    .seq {
        @apply relative left--10 top-15px ml-20px text-3xl text-white rounded-full flex items-center pl-40px pr-40px pt-15px pb-15px color-black; /* 增大尺寸 */
        background-image: url("https://images.jieyu.ai/images/hot/blue-purple-gradient.jpg");
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
        font-weight: 500;
        width: fit-content;
        font-size: 4.5vw; /* 明确设置更大的字体 */
    }

    .desc {
        position: absolute;
        bottom: 4rem;
        right: 1rem;
        @apply text-xl rounded-10px;
        text-align: left;
        opacity: 0.8;
        max-width: 50%;
    }

    /* Tags 样式 - 竖向排列在 title 下方 */
    .cover-tags {
        position: absolute;
        top: 75%; /* 位于 title 下方 */
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column; /* 竖向排列 */
        gap: 1rem; /* 增大间距 */
        align-items: center;
        z-index: 10;
    }

    .cover-tag {
        background: rgba(231, 76, 60, 0.9); /* 与主题色保持一致，添加透明度 */
        color: white;
        padding: 1rem 2rem; /* 增大内边距 */
        border-radius: 1.5rem; /* 增大圆角 */
        font-size: 4vw; /* 增大字体 */
        font-weight: 600; /* 增加字重 */
        display: inline-block;
        white-space: nowrap;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4); /* 增强阴影 */
        backdrop-filter: blur(6px);
        transition: all 0.3s ease;
        border: 2px solid rgba(255, 255, 255, 0.3); /* 增强边框 */
        min-width: 200px; /* 设置最小宽度保持一致 */
        text-align: center;
    }

    .cover-tag:hover {
        transform: translateY(-3px) scale(1.05); /* 增强悬停效果 */
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
        background: rgba(231, 76, 60, 1);
    }
}
</style>
<template>
    <div class="slidev-layout cover">
        <div v-if="$slidev.configs.seq" class="seq">{{ $slidev.configs.seq }}</div>
        <div v-if="$slidev.configs.motto" class="motto">{{ $slidev.configs.motto }}</div>

        <div class="top-55% title" v-html="$slidev.configs.title"></div>
        <div v-if="$slidev.configs.desc" class="desc text-center p-2vw color-white bg-#202020 box-shadow-2xl"
            v-html="$slidev.configs.desc">
        </div>

        <!-- Tags 显示在右下角 -->
        <div v-if="tags && tags.length > 0" class="cover-tags">
            <span v-for="tag in tags" :key="tag" class="cover-tag">{{ tag }}</span>
        </div>
    </div>
</template>
