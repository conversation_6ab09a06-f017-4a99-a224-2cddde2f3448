:root {
    --slidev-theme-primary: #404040;
    /* --slidev-theme-secondary: #E60000; */
    --slidev-theme-secondary: #606060;
    --slidev-background-color: #fcfcfc;
    font-family: <PERSON><PERSON><PERSON><PERSON>song, sans-serif !important;

    .abs {
        position: absolute;
    }

    /* counter-reset: section; */
}

.slidev-layout {
    text-justify: inter-character;
    text-align: justify;
    @apply h-full text-left pt-20 pb-30 text-$slidev-theme-primary;

    p+h2,
    ul+h2,
    table+h2 {
        @apply mt-10;
    }

    h1 {
        font-size: 8vw;
        color: var(--slidev-theme-secondary);
        margin: 10vw 0;
        line-height: 10vw;
        text-align: center;
    }


    h2 {
        text-align: left;
        font-size: 6vw;
        line-height: 8vw;
        font-weight: 500;
        margin: 6.4vw 0vw;
        padding: 0.5vw 1vw;
        /* counter-increment: section; */
    }

    /* h2:before {
        content: '/' counter(section, decimal-leading-zero) ' ';
    } */

    h3 {
        text-align: left;
        font-size: 5vw;
        font-weight: 300;
        margin: 5.2vw 0vw;
    }

    h4,
    h5 {
        text-align: left;
        font-size: 4.5vw;
        font-weight: 200;
        margin: 4.5vw 0vw;
    }

    a {
        color: #0000e6;
    }

    b {
        font-weight: 400;
    }

    p {
        font-size: 3.5vw;
        margin: 2.8vw 0vw;
        line-height: 5.5vw;
    }

    .admonition p {
        font-size: 3.5vw !important;
        line-height: 4.2vw;
    }

    .admonition li {
        font-size: 3.5vw !important;
        margin-top: 4.2vw;
    }

    li {
        font-size: 3.2vw;
        margin-bottom: 1vw;
    }

    red {
        display: inline;
        color: color-mix(in srgb, #E60000, #000 15%);
    }

    b {
        display: inline;
        font-weight: 500;
        color: color-mix(in srgb, var(--slidev-theme-secondary), #000 30%);
    }

    claimer {
        display: block;
        text-align: center;
        color: #a8a8a8;
    }

    cap {
        display: inline-block;
        width: 100%;
        text-align: center;
        font-size: 2.8vw;
        color: grey;
        font-style: italic;
        position: relative;
        top: -1em;
    }

    remark {
        color: #787878;
        font-style: italic;
        font-size: 3vw;
    }

    remark::before {
        content: ' ';
    }

    .katex {
        font-size: 3.5vw !important;
    }
}

.slidev-layout.intro {
    @apply h-full grid;

    h1 {
        color: inherit;
        @apply text-6xl leading-20;
    }
}


.slidev-layout.statement {
    @apply text-left h-full;
}

.slidev-layout.quote {
    @apply h-full;

    h1+p {
        @apply mt-2;
    }
}

.slidev-layout.section {
    h1 {
        @apply text-5xl font-500 leading-20 ml-10 mb-1;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-secondary), white) 1;
    }

    h1:after {
        content: unset;
    }

    h2 {
        @apply text-2xl font-300 ml-10;
    }

    h2:before {
        content: unset;
    }
}

.slidev-layout.center {

    h1,
    h2,
    h3,
    p {
        @apply text-center;
    }

    h1:after,
    h2:before,
    h3:before {
        content: unset;
    }

    h1 {
        @apply text-6xl font-500 leading-20 ml-10 mb-6 text-center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to left, white 0%, var(--slidev-theme-primary) 50%, white 100%)1;
    }
}
