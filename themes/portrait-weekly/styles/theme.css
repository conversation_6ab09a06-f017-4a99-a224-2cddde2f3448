.slidev-layout {
    ol {
        padding: 0;
        margin-left: 4vw;
        text-align: left;
    }

    img {
        width: calc(100%);
        margin: 0 auto;
        display: block;
    }

    img[alt="25%"] {
        width: 25%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="33%"] {
        width: 33%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="50%"] {
        width: 50%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="66%"] {
        width: 66%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="75%"] {
        width: 75%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }


    img[alt="100%"] {
        width: 75%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="R50"] {
        position: relative;
        float: right;
        width: 45%;
        margin: 1vw 0 0vw 2em !important;
    }

    img[alt="R33"] {
        position: relative;
        margin-top: 0;
        float: right;
        width: 33%;
        margin: 1vw 0 0vw 2em !important;
    }

    img[alt="L50"] {
        position: relative;
        float: left;
        width: 45%;
        margin: 1vw 2em 0 0 !important;
    }

    img[alt="L33"] {
        position: relative;
        float: left;
        width: 33%;
        margin: 1vw 2em 0 0 !important;
    }



    about {
        white-space: pre-wrap;
        font-size: 0.9em;
        margin: 4em 2em 2em 2em;
        display: block;
        color: #b0b0b0;
    }

    about:before {
        content: 'QuanTide Weekly 是公众号 QuanTide【量化风云】每周制作的资讯汇总，作为粉丝福利，每周日群内推送。包含量化策略、免费资源、教程和精选文章（含部分付费文章）。\A\A 除已声明第三方版权外，其余内容版权归公众号量化风云(QuanTide)所有。通过在线版本管理工具存证。允许保持原样传播，保留所有其它权利。';
    }
}
