# Getting started with Neversink

The theme depends on Node.js and [Slidev](https://sli.dev). If you don't have Node.js installed, you can download it from [nodejs.org](https://nodejs.org/). Once you have Node.js installed, you can create a new Slidev project with the Neversink theme by running the following command:

```bash
npm init slidev@latest
```

Then answer the sequence of questions. When it asks for the theme select `neversink`.

Alternatively if you already have installed Slidev globally you can just create a slidev markdown file (e.g., `slides.md`) and add the theme to the frontmatter of your first slide:

```md
---
theme: neversink
---
```

Then you are basically ready to go!

If you are new to Slidev highly recommend you check out the [Slidev documentation](https://sli.dev/) before diving in.

## Read on about all the Neversink features

- [Markdown features](markdown.md) - special addons to the Slidev markdown syntax
- [Color schemes](colors.md) - the color schemes available in Neversink
- [Branding](branding.md) - how to customize the theme to your brand/logos
- [Styling](styling.md) - the custom CSS classes available in Neversink
- [Custom layouts](layouts.md) - the custom slide layouts available in Neversink
- [Components](components.md) - the custom components such as sticky notes and admonitions
- [Customizing](customizing.md) - how to customize the theme with your own CSS/fonts, etc...

## ... or simply pick a layout to learn how to structure it

<!--@include: ./parts/layoutpicker.md-->
