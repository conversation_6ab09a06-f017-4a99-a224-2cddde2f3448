# Color Schemes

The project uses tailwind-like color schemes arranged in ==monochromatic pairs==.
Color schemes can be applied to several elements, perhaps most importantly to
slide [layouts](/layouts).

These boxes show the options and names:

## B&W Schemes

<div class="text-white bg-black pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">black</div>
<div class="text-black bg-white border-1 border-solid border-black pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">white</div>
<div class="text-gray-100 bg-gray-800 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">dark</div>
<div class="text-gray-800 bg-gray-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">light</div>

## Light Schemes

<div class="bg-red-100 text-red-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">red-light</div>
<div class="bg-orange-100 text-orange-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">orange-light</div>
<div class="bg-amber-100 text-amber-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">amber-light</div>
<div class="bg-yellow-100 text-yellow-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">yellow-light</div>
<div class="bg-lime-100 text-lime-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">lime-light</div>
<div class="bg-green-100 text-green-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">green-light</div>
<div class="bg-emerald-100 text-emerald-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">emerald-light</div>
<div class="bg-teal-100 text-teal-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">teal-light</div>
<div class="bg-cyan-100 text-cyan-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">cyan-light</div>
<div class="bg-sky-100 text-sky-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">sky-light</div>
<div class="bg-blue-100 text-blue-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">blue-light</div>
<div class="bg-indigo-100 text-indigo-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">indigo-light</div>
<div class="bg-violet-100 text-violet-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">violet-light</div>
<div class="bg-purple-100 text-purple-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">purple-light</div>
<div class="bg-pink-100 text-pink-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">pink-light</div>
<div class="bg-rose-100 text-rose-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">rose-light</div>
<div class="bg-fuchsia-100 text-fuchsia-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">fuchsia-light</div>
<div class="bg-slate-100 text-slate-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">slate-light</div>
<div class="bg-gray-100 text-gray-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">gray-light</div>
<div class="bg-zinc-100 text-zinc-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">zinc-light</div>
<div class="bg-neutral-100 text-neutral-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">neutral-light</div>
<div class="bg-stone-100 text-stone-500 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">stone-light</div>

## Regular Schemes

<div class="bg-red-500 text-red-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">red</div>
<div class="bg-orange-500 text-orange-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded  font-size-6 fw-700">orange</div>
<div class="bg-amber-500 text-amber-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">amber</div>
<div class="bg-yellow-500 text-yellow-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">yellow</div>
<div class="bg-lime-500 text-lime-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">lime</div>
<div class="bg-green-500 text-green-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">green</div>
<div class="bg-emerald-500 text-emerald-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">emerald</div>
<div class="bg-teal-500 text-teal-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">teal</div>
<div class="bg-cyan-500 text-cyan-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">cyan</div>

<div class="bg-sky-500 text-sky-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">sky</div>
<div class="bg-blue-500 text-blue-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">blue</div>
<div class="bg-indigo-500 text-indigo-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">indigo</div>
<div class="bg-violet-500 text-violet-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">violet</div>
<div class="bg-purple-500 text-purple-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">purple</div>
<div class="bg-pink-500 text-pink-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">pink</div>
<div class="bg-rose-500 text-rose-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">rose</div>
<div class="bg-fuchsia-500 text-fuchsia-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">fuchsia</div>
<div class="bg-slate-500 text-slate-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">slate</div>
<div class="bg-gray-500 text-gray-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">gray</div>
<div class="bg-zinc-500 text-zinc-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">zinc</div>
<div class="bg-neutral-500 text-neutral-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">neutral</div>
<div class="bg-stone-500 text-stone-100 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">stone</div>

## Add-ons

These are non-tailwind colors that are used in the project:

<div class="text-gray-300 bg-navy-900 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">navy</div>
<div class="bg-gray-50 text-navy-900 pt-3 pb-3 pl-3 pr-3 m-1 rounded font-size-6 fw-700">navy-light</div>

## Applying Schemes

Each scheme sets the following CSS vars:

```css
--neversink-bg-color
--neversink-bg-code-color
--neversink-fg-code-color
--neversink-fg-color
--neversink-text-color
--neversink-border-color
--neversink-highlight-color
```

which contains values for these options which might go well together in a monochromatic scheme.

To apply the theme to a element you simply add the `neversink-{name}-scheme` class to the element and then add another class which binds the CSS vars as you like.

For example, to apply the `red` scheme from above to a `div` element you would add the following classes:

```html
<div class="neversink-red-scheme colorbinding">This is a red div</div>
```

and then define the color binding class

```css
.colorbinding {
  background-color: var(--neversink-bg-color);
  color: var(--neversink-text-color);
  border-color: var(--neversink-border-color);
}
```

This provides you flexibility in how you decided to bind elements of the color scheme to your elements.
