{"name": "slidev-theme-neversink", "version": "0.2.0", "author": "gure<PERSON>s", "type": "module", "keywords": ["slidev-theme", "slidev"], "engines": {"node": ">=18.0.0", "slidev": ">=0.49.16"}, "dependencies": {"@iconify-json/logos": "^1.1.42", "@iconify-json/mdi": "^1.1.66", "@iconify-json/twemoji": "^1.1.15", "@iconify-json/uim": "^1.1.9", "@iconify/json": "^2.2.221", "@iconify/vue": "^4.1.2", "@mdit/plugin-sub": "^0.12.0", "@slidev/types": "^0.49.16", "markdown-it-mark": "^4.0.0", "qrcode.vue": "^3.4.1"}, "repository": {"type": "git", "url": "git+https://github.com/gureckis/slidev-theme-neversink.git"}, "bugs": {"url": "https://github.com/gureckis/slidev-theme-neversink/issues"}, "homepage": "https://gureckis.github.io/slidev-theme-neversink/", "slidev": {"colorSchema": "light", "highlighter": "all"}, "devDependencies": {"pnpm": "^9.4.0", "vitepress": "^1.2.3", "@slidev/cli": "^0.49.16", "playwright-chromium": "^1.45.0"}, "scripts": {"build": "slidev build example.md -o docs/public/example --base /slidev-theme-neversink/example/", "dev": "slidev example.md", "export": "slidev export example.md", "screenshot": "slidev export screenshot.md --format png --output docs/public/screenshots/", "screenshot:dev": "slidev screenshot.md", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "ci:publish": "pnpm publish --access public --no-git-checks"}}