---
title: 我们将达成什么样的目标？
layout: default
clicks: 3
---

<style scoped>
.wrapper {
    width: 50%;
    height: 100%;
    left:0;
    top:0;
    position:absolute;
    font-size: 0.8em;

    z-index: -2;
    background: linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.3)), url(https://images.unsplash.com/photo-1528460033278-a6ba57020470?q=80&w=2235&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D);
    background-size: cover;
}

.object {
    position: absolute;
    font-weight: 500;
    width: 70%;
    padding: 0.5em 1em;
    border-radius: 1em;
    background: linear-gradient(to right, rgba(130,183,198,0.9), rgba(100,150,170,0));
    box-shadow: 1px 2px 2px rgba(0,0,0,0.5);
}

</style>

<div class="wrapper">

<p class="object top-20%" v-motion :enter="{scale:1,rotateZ:-15}">🔍 会用 Alphalens</p>
<p class="object top-30% right-0" v-motion :enter="{scale:0}" :click-1="{scale:1, rotateZ:-15}">📈 会读 Alphalens 报表</p>
<p class="object top-55%" v-motion :enter="{scale:0}" :click-2="{scale:1, rotateZ:-15}">⛏️ 览遍 400+因子，掌握因子挖掘的方法和技术</p>
<p class="object top-65% right-0" v-motion :enter="{scale:0}" :click-3="{scale:1, rotateZ:-15}">💻 掌握机器学习、聚类模型、XGBoost 模型</p>

</div><!--wrapper-->

<div class="abs left-52% w-50% h-100%">

<Iframe src="https://www.jieyu.ai/articles/coursea/factor-ml/syllabus.html" w="100%"/>
</div>

<!--
1. 第1个目标，就是要会用Alphalens

这是一个著名的开源因子分析框架。

所谓会用，就是要懂得如何使用它的API，以完成因子检验过程

并且懂得它的实现原理，

并且懂得如何排除调错误

这是第2章到第4章的内容
   
[click]

当我们调用Alphalens完成一次因子检验时，

Alphalens就会生成许多报表。

如何解读这些报表？
什么样的报表意味着因子是有效的？

什么样的报表则意味着因子无效？

报表中的指标究竟是如何计算的，跟我们想像中的一样吗？指标的单位又是多少？如果从报表上看，因子的表现不太理想，是不是就应该放弃这个因子？还是说，存在进一步挖掘的可能？

我们会在第5~7章回答这些问题。

[click]

每个因子都可能有自己的局限，它们可能对收益有所贡献，

但单个因子的贡献可能很难满足我们对资产管理的预期。

比如，有的因子是黑天鹅，一旦出现，收益确定性强，但交易机会很少；

有的交易品种，会有自己独特的因子（比如像ETF，夏普，calmar这样的策略评估因子本身就可以当成选股因子）

像这样的因子，我们就得找很多，才能提高资金利用率。

如何挖掘新的因子呢？这是一个创新的活。所有的创新，首先都要从熟悉行业的现状开始，才能站在巨人的肩膀上。


所以我们会介绍大约400+个因子，其中约350多个，是大家学完之后就可以立即使用的。我们会先介绍Alpha101，这也是很多机构当成付费因子的一个因子库。这一章我们会介绍如何读懂Alpha101因子的公式，解读几个因子的构造原理，最后是介绍一个开源的实现。

另一个付费因子库是Alpha191，这是国泰君安团队提出的。我们也会介绍一个开源的实现给大家，也包括国泰君安的研报。

这是第8章的内容。这一课学完，你就有了接近300个因子。

第9章我们会介绍技术指标因子。这一章我们会介绍talib中的18个左右的技术指标，会选择几个指标讲一下它的交易原理、如何改造、翻新技术指标，我想这一点非常重要。

很多人会把技术指标直接拿来当成因子，并且使用传统的参数和阈值。这样做仍然有用，但可能不是最有效的。

我们要理解技术指标提出的时代背景以及它面向的用户。技术指标之所以以这样的形态、参数和阈值出现，是有它的时代背景的。现在我们有了更好的计算资源和算法，应该尝试在新的时代背景下创造性地运用它们，生成所谓的“智能”技术指标。

第10章我们会介绍更多的因子。这里有黑天鹅因子、策略评估类因子、导数因子、频域因子、行为金融学因子。通过这一章，我们想尽可能地打开大家的思路，了解更多的技术和算法，这对未来大家自己挖掘因子会有帮助。

第11章我们会介绍基本面因子和另类因子。在另类因子这一部分，我们主要讲思路。另类因子往往依赖爬虫技术，我们会介绍几种技术路线的比较和选择，但不会去实现一个爬虫。

第12章我们会谈谈因子挖掘的方法论、相关资源。因子挖多了，就有可能出现重复，我们也会介绍如何进行因子正交性检测。这样就结束了第二部分

[click]

从第13章开始，我们将介绍机器学习。

首先会介绍机器学习中的一些基础理论。机器学习中的基础理论和先修知识还是很多的，我们主要介绍跟应用层密切相关的那部分知识，比如损失函数和评估函数有什么区别？了解这个，在为模型选择参数时，才会知道哪些函数可以当成损失函数，哪些可以当成评估函数。

更基础、更底层的概念，比如梯度优化、反向传播、矩阵等，这些就不涉及了。

这是第13章、第14章的内容。

在对机器学习有了一个概观之后，我们会介绍sklearn这个库。sklearn 是一个非常强大的机器学习库，它有丰富的模型，接口简单易用，深受大家喜爱。

这个库的内容比较多，我们在第15章里，先介绍一些通用工具包，这部分内容，会在数据预处理、模型评估、解释与可视化等场合下使用。

第16章我们介绍模型优化的一般知识，包括了k-fold cross validation, 参数搜索等。这里我们还要指出一个与机器学习课程的不同之处，我们在金融领域可能更多地会使用rolling forecasting,而不是cross validation。

到这一章讲完，我们就完整地掌握了机器学习在应用层面所有的知识。接下来，我们就看三个具体的模型，解决具体的问题，也是我们学这门课会得到的具体的收获。
-->
