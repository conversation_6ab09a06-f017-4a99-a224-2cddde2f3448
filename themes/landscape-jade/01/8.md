---
title: 如何有效地利用本课程
clicks: 4
---

<style scoped>

.screen-shot {
    box-shadow: 1px 2px 5px rgba(0,0,0,0.2);
    margin-right: 20px;

}
</style>

<div class="abs top-20%">

<div v-motion :enter="{opacity:1}">
<h2>教材</h2>
</div>

<div v-motion :enter="{opacity:0}" :click-1="{opacity:1}">
<h2>直播/录播视频</h2>
</div>

<div v-motion :enter="{opacity:0}" :click-2="{opacity:1}">
<h2>练习</h2>
</div>

<div v-motion :enter="{opacity:0}" :click-3="{opacity:1}">
<h2>辅导与答疑</h2>
</div>

<div v-motion :enter="{opacity:0}" :click-4="{opacity:1}">
<h2>学期时长</h2>
</div>
</div>



<div class="abs h-full w-50% left-45% flex flex-row top-20%">

<div v-motion :enter="{y: 0}" :click-1="{y: -720}" class="abs">
<img src="https://images.jieyu.ai/images/2024/09/jupyter-notebook-page.jpg?1" class="screen-shot"/>
</div>

<div v-motion :enter="{y:720}" :click-1="{y: 0}" :click-2="{y: -720}" class="abs">
<img src="https://images.jieyu.ai/images/hot/course/factor-ml/video-cover.jpg" class="screen-shot"/>
</div>

<div v-motion :enter="{y:720}" :click-2="{y: 0}" :click-3="{y: -720}" class="abs">
<img src="https://images.jieyu.ai/images/2024/09/nbgrader.jpg" class="screen-shot"/>
</div>

<div v-motion :enter="{y:720}" :click-3="{y: 0}" :click-4="{y: -720}" class="abs">
<img src="https://images.jieyu.ai/images/2024/09/one-on-one-tutor.jpg" class="screen-shot"/>
</div>

<div v-motion :enter="{y:720}" :click-4="{y: 0}" :click-5="{y: -720}" class="abs">
<img src="https://images.jieyu.ai/images/2024/09/online-webinar.jpg" class="screen-shot"/>
</div>

</div>


<!--

讲到这里，有的同学可能要问，老师，你今天没照着书讲啊。

好，现在我就来介绍下，我们这门课在教学上的特点。

课程由4部分交付。

首先是教材。

我们的教材都是jupyter notebook格式的，并且通过myst和自己的脚本进行了格式增强，所以文档很美观，同时还有一些额外的功能。

比如我们的图、代码都有编号；有脚注；在一些重点地方，我们会提供各种tip, warning等所谓的admonition结构来特别引起大家的注意。

[click]

第二部分是视频，就是大家现在看到的内容。我们使用了 slidev 来制作演示文稿，大家已经看到了，我们这一版跟早期课程相比，视觉效果是比较吸引人的。

制作这些演示文稿确实很花时间，但我希望，我花更多的时间，可以让你在听课时，精神更为兴奋，注意力更加集中，让听课时间不浪费。

在内容区分上，视频会参考教材，但不会照搬教材。教材的内容大而全，照着读，跟着练，是可以独立运行的。

那视频讲解干什么？

主要是讲重点、讲难点、讲故事。

重点就是对应教材的核心部分；难点就是，我们可能一部分同学代码的功底不够好。

就算是代码功底好的，面对陌生的库、陌生的语法，我们多讲一点，大家学习起来就快点。

学习需要激励、所以我也会尽可能地讲一些故事，当然这部分可能讲得不太好；

教材的作用我刚刚讲过了。这门课是先有教材，再做的视频。

[click]

第三部分就是课程作业。作业的内容是什么？比如，可能某个问题有几种方案，在教材中我们介绍了其中一种，对另一个方案，就只是简单地提了一下。那么就可能留一个作业，让你在练习中去完成这个方案，起到举一反三的作用。

比如在因子分析的预处理中，我们可能要去缺失值。最简单的就是把nan的记录都drop掉，这也是Alphalens在使用的方法。我们在课程中也提到，还有取行业平均值的做法，但没有给实现。

我们是通过作业，让你自己去实现，但我们都已经写好了答案。你提交作业之后，就可以看到答案。

[click]

第4部分是老师1：1的辅导。这部分只对购买本课程的同学提供。其它同学可以在大群里提问，如果我看到，也会回答。

[click]

这门课直播期大概是两到三个月。时间定在每周日晚8点。这样安排的话，大家周末可以安排出行计划，因为周一上班，所以，周日晚一般能保证在家。

如果错过直播，也可以在荔课上收看录播。对24课的老学员，你们收看录播的方法是，找宽粉要一个链接，在微信上点击进入，但不要购买。再说一次，不要购买。然后退出，跟宽粉讲一下，她会在后台为你开通观看权限。

-->
