---
title: 作业系统 - NbGrader 介绍
clicks: 2
---

<Iframe src="http://***************:5180/student_fa/lab?" top="10%" w="100%"/>


<v-drag v-motion class="abs left-10% top-10% w-200px h-80px" pos="243,45,200,80"
    :enter="{scale:0}"
    :click-1="{scale:1}"
    :click-2="{scale: 0}">
<Ellipse class="abs w-200px h-80px"/>
</v-drag>

<!--
在本课程中，我们使用了先进的教学技术，比如，每个人的课程环境都是独立的容器；演示文稿我们使用了slidve；也包括使用 NbGrader 这个作业系统。

一些非常著名的大学，比如ucberkley，他们的学生作业布置就是用的这个系统。

在介绍作业系统之前，我先带大家了解下课程环境的目录结构。


下面我们介绍下如何使用这个系统。作为课程准备的一部分，你需要尽快熟悉如何在本课程中访问数据。

我们提供了一个辅助阅读材料，在supplements目录下，你应该在第2课之前，先熟悉它的内容。

[demo] data.ipynb

[click]

为了确保你熟悉其内容，我们安排了一个作业。

通过assignment list来获取我们部置的作业。

新作业 > Fetch
下载后的作业 > 完成 > submit
已提交的作业 > 进看feedback

[demo] 介绍 01-data-access.ipynb

-->
