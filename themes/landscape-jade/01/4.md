---
title: 我们将收获什么？
layout: default
clicks: 3
---

<style scoped>
.wrapper {
    position: absolute;
    width: 50%;
    height: 100%;
    top:0;
    left: 50%;

    z-index: -2;
    background: linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.3)), url(https://images.unsplash.com/photo-1528460033278-a6ba57020470?q=80&w=2235&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D);
    background-size: cover;
}

.object {
    position: absolute;
    font-size: 0.8em;
    font-weight: 500;
    width: 60%;
    padding: 0.5em 1em;
    border-radius: 1em;
    background: linear-gradient(to right, rgba(130,183,198,0.9), rgba(100,150,170,0));
    box-shadow: 1px 2px 2px rgba(0,0,0,0.5);
}

</style>

<div class="wrapper">
<p class="object top-10%" v-motion :enter="{scale:1, rotateZ:15}">📚 带走 350+因子实现</p>
<p class="object top-35% left-30%" v-motion :enter="{scale:0}" :click-1="{scale:1, rotateZ:15}">🧩 聚类算法寻找配对资产</p>
<p class="object top-45%" v-motion :enter="{scale:0}" :click-2="{scale:1, rotateZ:15}">💰 基于 XGBoost 的资价格预测模型</p>
<p class="object top-70% left-30%" v-motion :enter="{scale:0}" :click-3="{scale:1, rotateZ:15}">💲 基于 XGBoost 的趋势交易模型</p>
</div>

<div class="abs left-0 w-50% h-100%">

<Iframe src="https://www.jieyu.ai/articles/coursea/factor-ml/syllabus.html#17-聚类寻找-pair-trading-标的" w="100%"/>
</div>

<!--

第一个是我们将收获350+的因子。

[click]

在这门课中，我们将学习到的第一个具体的策略就是配对交易。

A股现在有5000多支资产。随机两两配对，我们要搜索的次数达到1200多万次，其中绝大多数是无效的搜索。

如何使得这个过程更有效一些呢？

我们可以通过聚类算法，将海量资产先划分成为一个个小的簇，然后在组内通过协整性检验来找到配对。

我们将介绍聚类算法，它是无监督学习这一大类中的一种。

这是第17章的内容。除了用来寻找配对资产，聚类算法本身也有划分板块的能力。这方面可能潜藏着一些机会。比如，当出现一支连板股之后，你会不会想知道，有哪些股跟它是一类的？

[click]

我们要收获的第二个模型，就是基于XGBoost的资产定价模型。资产定价是量化研究的核心问题之一，如果能够给出资产的合理定价，那么就能给出交易信号。

实现端到端的价格预测模型比较有挑战，但是，我们还是可以通过一些变换，做出来一个价格预测模型。

大家相信这是可能的吗？有人相信吗？相信的人可以在屏幕上扣个1。

答案是肯定的。我们刚刚讲过的配对交易，它正是基于价格很难预测，所以就通过资产配对，将价格的预测转换为价格差的预测。前者不是平稳序列，后者是，至少是带条件是，对吧？

既然有第一个例子，肯定就会有第二个例子。我相信，在学完这门课程之后，你也会认为，在特定的时间段和标的上，价格是可以预测的；

接下来，你就可以继续研究，如何去扩大它的运用范围。那么，以此为出发点，你在机器学习交易模型构建上，就已经占据了领先优势。

我们介绍的这个模型是一个回归模型。

[click]

然后我们还会收获一个分类模型。换句话说，它不负责预测价格，而是直接告诉你交易信号。注意这跟资产定价模型是有区别的，同意吗？

资产定价模型是，你能给出定价，我知道现价，就有了交易信号；我们这里给出的模型是，我不知道定价是多少，但能直接给出交易信号。

听起来比较神奇，对吧？这可能吗？回想下配对交易。它能不能给出资产定价？不能。但它也能给出交易信号，对不对？

所以，我们要实现的模型，理论上也是成立的。

这就是第19、20章的内容。我估计这两个模型在比较长的时间内都是有优势的；留给你的工作就是构建系统、标注数据、提取特征，训练和优化模型，在不同的资产上进行测试而已。

XGBoost是一种梯度提升决策树。在第20章，我们还要介绍另一个实现，即lightGBM。然后我们还要对算法进行一些总结和再思考。

第22章是结束语。在课程中，我们只讲到机器学习构建策略，没有提到神经网络。在这一章，我们就介绍为何如此安排。多年前我对CNN网络来做交易进行了一些粗浅的研究，了解神经网络在构建交易策略上的困难所在，也会把这些经验分享给大家。

这并不必然意味着使用神经网络来构建交易策略不可行，只是从成本代价上看是否适合的问题。另外，我觉得直接使用现成的框架，结果不会太理想。

希望通过这一章，我们对机器学习、神经网络的在量化交易上的理解更加深入。那如果你未来以从事研究为主的话，希望这些内容对你有所启迪。

所以，在我们的课程中，主要是介绍了三个策略。

我们为什么选这三个策略？这三个策略，在量化交易策略中占有多大的比重？

要回答这个问题，我们得把量化交易策略的发展史介绍一下。

-->
