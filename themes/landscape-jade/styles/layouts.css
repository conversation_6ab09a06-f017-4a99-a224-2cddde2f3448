:root {
    --slidev-theme-primary: black;
    --slidev-theme-secondary: rgba(0, 0, 0, 0.6);
    counter-reset: h2counter;
}

* {
    /*禁止 ipad中，长按时出现context menu */
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    touch-action: pan-x pan-y;
}

svg.pointer-events-none {
    z-index: 100;
}

.abs {
    position: absolute;
}

.hide {
    display: none;
}

.slidev-presenter .slidev-page {
    border: 1px solid black;
}

.slidev-layout {
    @apply h-full text-$slidev-theme-primary;
    /* background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f046, rgb(86, 134, 136, 0.9)); */
    /* animation: background-anime 3s ease infinite; */
    padding: 0;

    p+h2,
    ul+h2,
    table+h2 {
        @apply mt-10;
    }

    * {
        text-justify: inter-character;
    }

    ul {
        list-style-type: none;
    }

    h1 {
        /*this is title*/
        @apply text-4xl text-left;
    }

    h2 {
        color: cadetblue;
        @apply text-4xl text-left mb-4 ml-2;
    }

    h3 {
        color: rgb(86, 134, 136);
        @apply text-2xl text-left ml-8 mb-4;
    }

    h4 {
        @apply text-xl text-left ml-18 mb-2;
    }

    h5 {
        @apply text-base;
    }

    h6 {
        @apply text-sm pt-1 uppercase tracking-widest font-500 -ml-[0.05em];
    }

    h6:not(.opacity-100) {
        @apply opacity-40;
    }

    hr {
        content: ' ';
        display: block;
        width: 98%;
        position: absolute;
        top: 11vh;
        left: 1vw;
        border-style: none;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-primary) 0%, white 50%, white 100%) 0.5;
    }
}

@keyframes background-anime {
    0% {
        background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f046, rgb(86, 134, 136, 0.9));
    }

    50% {
        background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f066, rgb(86, 134, 136, 0.9));
    }

    100% {
        background-image: linear-gradient(45deg, rgba(95, 158, 160, 0.8), #f0f0f086, rgb(86, 134, 136, 0.9));
    }
}

.slidev-layout.cover {
    @apply h-full grid;

    h1 {
        @apply text-6xl text-left h-80 c-white ml-2;
    }

    h1:after {
        border-bottom: none;
    }
}

.slidev-layout.intro {
    @apply h-full grid;

    h1 {
        color: inherit;
        @apply text-6xl leading-20;
    }
}


.slidev-layout.statement {
    @apply text-center grid h-full;
}

.slidev-layout.quote {
    @apply grid h-full;

    h1+p {
        @apply mt-2;
    }
}

.slidev-layout.section {
    h1 {
        @apply text-5xl font-500 leading-20 ml-10 mb-1;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to right, var(--slidev-theme-primary), white) 1;
    }

    h1:after {
        content: unset;
    }

    h2 {
        @apply text-2xl font-300 ml-10;
    }

    h2:before {
        content: unset;
    }
}

.slidev-layout.center {

    h1,
    h2,
    h3,
    p {
        @apply text-center;
    }

    h1:after,
    h2:before,
    h3:before {
        content: unset;
    }

    h1 {
        @apply text-6xl font-500 leading-20 ml-10 mb-6 text-center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-image: linear-gradient(to left, white 0%, var(--slidev-theme-primary) 50%, white 100%)1;
    }
}

.slidev-layout.image-left {
    h1 {
        position: relative;
        left: -108%;
    }
}

run::before {
    content: ' 运行 ';
}

run {
    font-size: 1.2em;
    color: red;
    font-weight: 700;
}

draw::before {
    content: ' 手绘 '
}

draw {
    font-size: 1.2em;
    color: red;
    font-weight: 700;
}
