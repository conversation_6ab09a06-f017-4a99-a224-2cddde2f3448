.slidev-layout {
    ol {
        padding: 0;
        margin-left: 4vw;
        text-align: left;
    }

    img {
        width: calc(100%);
        margin: 0 auto;
        display: block;
    }

    img[alt="25%"] {
        width: 25%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="33%"] {
        width: 33%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="50%"] {
        width: 50%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="66%"] {
        width: 66%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="75%"] {
        width: 75%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }


    img[alt="100%"] {
        width: 75%;
        margin-left: auto;
        margin-right: auto;
        display: block;
    }

    img[alt="R50"] {
        position: relative;
        float: right;
        width: 45%;
        margin: 0 2vw;
    }

    img[alt="R33"] {
        position: relative;
        float: right;
        width: 30%;
        margin: 0 2vw;
    }

    img[alt="L50"] {
        position: relative;
        float: right;
        width: 45%;
        margin: 0 2vw;
    }

    img[alt="L33"] {
        position: relative;
        float: right;
        width: 30%;
        margin: 0 2vw;
    }

    .z-table {
        background: white;
        border-radius: 3px;
        border-collapse: collapse;
        margin: auto;
        padding: 5px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        animation: float 5s infinite;

        th {
            color: #D5DDE5;
            background: #1b1e24;
            border-bottom: 1px solid #9ea7af;
            border-right: 1px solid #343a45;
            font-size: 1.1em;
            font-weight: 100;
            text-align: center;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
            vertical-align: middle;
        }

        th:first-child {
            border-top-left-radius: 3px;
        }

        th:last-child {
            border-top-right-radius: 3px;
            border-right: none;
        }

        tr {
            border-top: 1px solid #C1C3D1;
            border-bottom: 1px solid #C1C3D1;
            color: #666B85;
            font-weight: normal;
            text-shadow: 0 1px 1px rgba(256, 256, 256, 0.1);
        }

        tr:hover td {
            background: #4E5066;
            color: #FFFFFF;
            border-top: 1px solid #22262e;
        }

        tr:first-child {
            border-top: none;
        }

        tr:last-child {
            border-bottom: none;
        }

        tr:nth-child(odd) td {
            background: #EBEBEB;
        }

        tr:nth-child(odd):hover td {
            background: #4E5066;
        }

        tr:last-child td:first-child {
            border-bottom-left-radius: 3px;
        }

        tr:last-child td:last-child {
            border-bottom-right-radius: 3px;
        }

        td {
            background: #FFFFFF;
            text-align: center;
            vertical-align: middle;
            font-weight: 300;
            text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.1);
            border-right: 1px solid #C1C3D1;
        }

        td:last-child {
            border-right: 0px;
        }

        th.text-left {
            text-align: left;
        }

        th.text-center {
            text-align: center;
        }

        th.text-right {
            text-align: right;
        }

        td.text-left {
            text-align: left;
        }

        td.text-center {
            text-align: center;
        }

        td.text-right {
            text-align: right;
        }
    }
}
