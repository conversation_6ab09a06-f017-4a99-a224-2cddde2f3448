---
theme: themes/portrait-flora
title: 测试标签功能
seq: 标签测试
motto: 测试圆角矩形标签
tags: [quantstats, python, finance, test]
layout: cover
---

# 测试标签功能

这是封面页，应该显示标签。

---
layout: default
---

<ApiCardCover category="基础统计" title="compsum()" :tags="['quantstats', 'python', 'cumulative']">
  <template #description>
    计算滚动复合收益率（累积乘积）- 将收益率序列转换为累积财富指数
  </template>
  <template #signature>
    compsum(returns)
  </template>
  <template #returns>
    pd.Series - 累积复合收益率序列
  </template>
  <template #params>
returns: 收益率序列
  </template>
  <template #example>
```python
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
# 输出: [0.01, -0.0098, 0.0207, 0.0105, 0.0307]
```
  </template>
</ApiCardCover>

---

<ApiCardBody title="快速示例" :tags="['example', 'demo']">
  <template #example>
```python
# 简单的累积收益计算
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
print(cumulative.tolist())
```
  </template>
  
  <template #output>
[0.01, -0.0098, 0.0207, 0.0105, 0.0307]
  </template>
</ApiCardBody>
