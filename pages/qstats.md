---
title: Quantstats：收益怎么算
seq: 最全Quantstats.stats指标系列
motto: 细节决定成败
tags: [compsum(), comp(), expected_return()]
---

---

<ApiCardCover category="基础统计" title="compsum()" :tags="['quantstats', 'python', 'finance']">
  <template #description>
    计算滚动复合收益率（累积乘积）- 将收益率序列转换为累积财富指数
  </template>
  <template #signature>
    compsum(returns)
  </template>
  <template #returns>
    pd.Series - 累积复合收益率序列
  </template>
  <template #params>
• returns: 收益率序列
• 格式: [0.01, -0.02, 0.03, ...] 表示1%, -2%, 3%
• 计算公式: (1 + returns).cumprod() - 1

  </template>
  <template #example>
```python
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
# 输出: [0.01, -0.0098, 0.0207, 0.0105, 0.0307]
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
# 输出: [0.01, -0.0098, 0.0207, 0.0105, 0.0307]
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)
# 输出: [0.01, -0.0098, 0.0207, 0.0105, 0.0307]

```
</template>
</ApiCardCover>

---

<ApiCardCover category="基础统计" title="comp()" :tags="['quantstats', 'total', 'return']">
  <template #description>
    计算总复合收益率（最终累积收益）- 整个期间的总收益率
  </template>
  <template #signature>
    comp(returns)
  </template>
  <template #returns>
    float - 总复合收益率
  </template>
  <template #params>
• returns: 收益率序列
• 计算公式: (1 + returns).prod() - 1
• 等同于 compsum() 的最后一个值
  </template>
  <template #example>
```python
# 示例用法
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
total_return = qs.stats.comp(returns)

print(f"总收益率: {total_return:.4f}")
# 输出: 总收益率: 0.0307

# 等价计算方法
cumulative = qs.stats.compsum(returns)
print(f"最后值: {cumulative.iloc[-1]:.4f}")
# 输出: 最后值: 0.0307
```
  </template>
</ApiCardCover>

---

<ApiCardCover category="基础统计" title="expected_return()" :tags="['quantstats', 'expected', 'geometric']">
  <template #description>
    计算期望收益率（几何平均数）- 基于历史数据的预期收益
  </template>
  <template #signature>
    expected_return(returns, aggregate=None, compounded=True, prepare_returns=True)
  </template>
  <template #returns>
    float - 期望收益率
  </template>
  <template #params>
• returns: 收益率序列
• aggregate: 聚合周期: 'D'(日), 'W'(周), 'M'(月), 'Q'(季), 'Y'(年)
• compounded: 是否使用复合收益率计算
• prepare_returns: 是否预处理数据（去除NaN等）
  </template>
  <template #example>
```python
# 示例用法
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])

# 计算日度期望收益
expected_ret = qs.stats.expected_return(returns)
print(f"日度期望收益: {expected_ret:.4f}")

# 计算月度期望收益
monthly_expected = qs.stats.expected_return(returns, aggregate='M')
print(f"月度期望收益: {monthly_expected:.4f}")

# 计算公式: (∏(1 + returns))^(1/n) - 1
# 几何平均数，考虑复利效应
```
  </template>
</ApiCardCover>

---

<ApiCardBody title="高级示例 - 投资组合分析" :tags="['portfolio', 'advanced', 'analysis']">

  <template #example>
```python
import pandas as pd
import quantstats as qs
import numpy as np

# 创建多个资产的收益率数据
dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
np.random.seed(42)

# 模拟三个资产的日收益率
stock_a = np.random.normal(0.0008, 0.02, len(dates))
stock_b = np.random.normal(0.0006, 0.015, len(dates))
stock_c = np.random.normal(0.0010, 0.025, len(dates))

# 创建DataFrame
portfolio_returns = pd.DataFrame({
    'Stock_A': stock_a,
    'Stock_B': stock_b,
    'Stock_C': stock_c
}, index=dates)
```
  </template>
</ApiCardBody>

---

续前一页。

<ApiCardBody title="投资组合分析 - 计算与结果" :tags="['portfolio', 'calculation', 'results']">

  <template #example>
```python
# 计算每个资产的累积收益
cumulative_returns = {}
for asset in portfolio_returns.columns:
    cumulative_returns[asset] = qs.stats.compsum(portfolio_returns[asset])

# 计算投资组合权重（等权重）
weights = [0.33, 0.33, 0.34]
portfolio_daily_returns = (portfolio_returns * weights).sum(axis=1)
portfolio_cumulative = qs.stats.compsum(portfolio_daily_returns)

print("投资组合累积收益率:")
print(f"最终累积收益: {portfolio_cumulative.iloc[-1]:.4f}")
print(f"年化收益率: {(1 + portfolio_cumulative.iloc[-1]) ** (252/len(dates)) - 1:.4f}")
```
  </template>

  <template #output>
投资组合累积收益率:
最终累积收益: 0.8234
年化收益率: 0.1876

各资产表现:
Stock_A: 0.7891 (78.91%)
Stock_B: 0.6543 (65.43%)
Stock_C: 0.9876 (98.76%)
  </template>
</ApiCardBody>
