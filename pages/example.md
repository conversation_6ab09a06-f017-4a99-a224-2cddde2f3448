---
theme: themes/portrait-flora
date: 2023-12-20
seq: 宽粉读研报
title: 已实现跳跃波动率
motto: 我们必须知道 我们终将知道
desc: "With CSS grid layout, the grid itself within its container as well as grid items can be positioned with the following 6 properties: justify-items, align-items, justify-content, align-content, justify-self, and align-self. These properties are part of the CSS box alignment module and they define a standard way to position elements with either flexbox or CSS grid"
layout: cover
---
<style>
.function-card {
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    margin: 15px;
    background-color: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: inline-block;
    width: 450px;
    vertical-align: top;
}

.function-card h3 {
    color: #2c3e50;
    margin-top: 0;
    font-size: 20px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.category {
    background-color: #3498db;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 12px;
}

.description {
    font-size: 15px;
    line-height: 1.5;
    color: #34495e;
    margin-bottom: 15px;
}

.signature {
    font-size: 13px;
    color: #7f8c8d;
    background-color: #ecf0f1;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 12px;
    font-family: 'Courier New', monospace;
    overflow-x: auto;
}

.returns {
    font-size: 14px;
    color: #27ae60;
    font-weight: bold;
    margin-bottom: 15px;
}

.param-details {
    background-color: #f8f9fa;
    border-left: 4px solid #3498db;
    padding: 12px;
    margin: 12px 0;
    border-radius: 0 6px 6px 0;
}

.param-item {
    margin-bottom: 10px;
    line-height: 1.4;
}

.param-name {
    font-weight: bold;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    background-color: #e8f4f8;
    padding: 2px 6px;
    border-radius: 3px;
}

.param-type {
    color: #8e44ad;
    font-style: italic;
    font-size: 12px;
    margin-left: 6px;
}

.param-desc {
    color: #34495e;
    margin-top: 4px;
    font-size: 13px;
}

.example-code {
    background-color: #1e3a8a;
    color: #f1f5f9;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    margin-top: 12px;
    overflow-x: auto;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.example-code .comment {
    color: #94a3b8;
    font-style: italic;
}

.example-code .output {
    color: #34d399;
    font-weight: bold;
}

/* 不同类别的颜色 */
.basic-stats { border-color: #e74c3c; }
.basic-stats .category { background-color: #e74c3c; }

.risk-metrics { border-color: #f39c12; }
.risk-metrics .category { background-color: #f39c12; }

.performance-ratios { border-color: #27ae60; }
.performance-ratios .category { background-color: #27ae60; }

.drawdown-analysis { border-color: #8e44ad; }
.drawdown-analysis .category { background-color: #8e44ad; }

.benchmark-comparison { border-color: #2980b9; }
.benchmark-comparison .category { background-color: #2980b9; }

.advanced-metrics { border-color: #34495e; }
.advanced-metrics .category { background-color: #34495e; }
</style>

---
layout: default
---

<div class="function-card basic-stats">
<div class="category">基础统计</div>
<h3>compsum()</h3>
<div class="description">计算滚动复合收益率（累积乘积）- 将收益率序列转换为累积财富指数</div>
<div class="signature">compsum(returns)</div>
<div class="returns">返回: pd.Series - 累积复合收益率序列</div>
<div class="param-details">
<div class="param-item">
<span class="param-name">returns</span><span class="param-type">(pd.Series, 推荐)</span>
<div class="param-desc">• 收益率序列，通常是日收益率数据<br>• 格式: [0.01, -0.02, 0.03, ...] 表示1%, -2%, 3%<br>• 计算公式: (1 + returns).cumprod() - 1</div>
</div>
</div>
<div class="example-code">
<span class="comment"># 示例用法</span>
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)

<span class="comment"># 输出结果</span>
<span class="output"># [0.01, -0.0102, 0.0195, 0.0093, 0.0295]</span>

<span class="comment"># 实际含义：</span>
<span class="comment"># 第1天: 1% 收益</span>
<span class="comment"># 第2天: (1.01 * 0.98) - 1 = -1.02%</span>
<span class="comment"># 第3天: (0.989 * 1.03) - 1 = 1.95%</span>
</div>
</div>

<div class="function-card basic-stats">
<div class="category">基础统计</div>
<h3>comp()</h3>
<div class="description">计算总复合收益率（最终累积收益）- 整个期间的总收益率</div>
<div class="signature">comp(returns)</div>
<div class="returns">返回: float - 总复合收益率</div>
<div class="param-details">
<div class="param-item">
<span class="param-name">returns</span><span class="param-type">(pd.Series, 推荐)</span>
<div class="param-desc">• 收益率序列<br>• 计算公式: (1 + returns).prod() - 1<br>• 等同于 compsum() 的最后一个值</div>
</div>
</div>
<div class="example-code">
<span class="comment"># 示例用法</span>
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
total_return = qs.stats.comp(returns)

print(f"总收益率: {total_return:.4f}")
<span class="output"># 输出: 总收益率: 0.0295</span>

<span class="comment"># 等价计算方法</span>
cumulative = qs.stats.compsum(returns)
print(f"最后值: {cumulative.iloc[-1]:.4f}")
<span class="output"># 输出: 最后值: 0.0295</span>
</div>
</div>


<div class="function-card basic-stats">
<div class="category">基础统计</div>
<h3>expected_return()</h3>
<div class="description">计算期望收益率（几何平均数）- 基于历史数据的预期收益</div>
<div class="signature">expected_return(returns, aggregate=None, compounded=True, prepare_returns=True)</div>
<div class="returns">返回: float - 期望收益率</div>
<div class="param-details">
<div class="param-item">
<span class="param-name">returns</span><span class="param-type">(pd.Series, 推荐)</span>
<div class="param-desc">• 收益率序列</div>
</div>
<div class="param-item">
<span class="param-name">aggregate</span><span class="param-type">(str, 可选)</span>
<div class="param-desc">• 聚合周期: 'D'(日), 'W'(周), 'M'(月), 'Q'(季), 'Y'(年)</div>
</div>
<div class="param-item">
<span class="param-name">compounded</span><span class="param-type">(bool, 默认=True)</span>
<div class="param-desc">• 是否使用复合收益率计算</div>
</div>
<div class="param-item">
<span class="param-name">prepare_returns</span><span class="param-type">(bool, 默认=True)</span>
<div class="param-desc">• 是否预处理数据（去除NaN等）</div>
</div>
</div>
<div class="example-code">
<span class="comment"># 示例用法</span>
returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
returns.index = pd.date_range(start='2020-01-01', periods=5, freq='B')

<span class="comment"># 计算日度期望收益</span>
expected_ret = qs.stats.expected_return(returns)
print(f"日度期望收益: {expected_ret:.4f}")

<span class="comment"># 计算月度期望收益</span>
monthly_expected = qs.stats.expected_return(returns, aggregate='M')
print(f"月度期望收益: {monthly_expected:.4f}")

<span class="comment"># 计算公式: (∏(1 + returns))^(1/n) - 1</span>
<span class="comment"># 几何平均数，考虑复利效应</span>
</div>
</div>

<!--

把 geometric_return 也加进来

还是从每日的简单收益出发。当我们有行情数据时，就可以很容易计算出标的的每日涨跌幅，也就是每日收益。

作为一个策略，我们可能想知道从T0日起，到T1, T2, ..Tn，我们的累积收益（复利）是多少。这就是compsum的由来。

compsum的结果是一个序列。它的最后一个值（comp)，就是该策略最后的净值减去1。或者说，compsum + 1，就是T0, T1, ...Tn日的策略净值。

在对两个策略比较收益能力时，我们可以将简单收益取平均再比较。但是这样没有考虑复利能力。如果要精确地考虑复利，我们就应该取几何收益平均。这就是 expected_return的由来。在quantstats中，还有一个 geometric_mean， 两者完全是一回事，只不过参数略有不同。

在quantstats中计算几何收益时，它要求returns数据一定要带有日期。否则无法计算。

以下代码演示了几种收益之间的联系。

```python
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates


np.random.seed(42)
days = 10
dates = pd.date_range(start="2023-01-01", periods=days)
daily_returns = np.random.normal(0.005, 0.01, days)


df = pd.DataFrame({
    "日收益": daily_returns
}, index=dates)


df["日收"] = (1 + df["日收益"]).cumprod()

mean_daily_return = df["日收益"].mean()
df["日收均值"] = (1 + mean_daily_return) **(np.arange(1, len(df)+1))

df["日收复合"] = qs.stats.compsum(df["日收益"]) + 1

expected_return = qs.stats.expected_return(df["日收益"])
geometric_return = qs.stats.geometric_mean(df["日收益"])

df["日收预期"] = (1 + expected_return)** (np.arange(1, len(df)+1))
df["日收几何"] = (1 + geometric_return) ** (np.arange(1, len(df) + 1))

# df[["累积净值_原始", "累积净值_均值", "累积净值_复合", "累积净值_预期"]].plot()
# 绘制图形
fig, ax = plt.subplots(figsize=(12, 6))

# 绘制四条曲线
ax.plot(df.index, df["日收"], label="简单收益累积", 
        marker="o", linestyle="-", color="blue")
ax.plot(df.index, df["日收均值"], label="按日均收益计算的累积净值", 
        marker="s", linestyle="--", color="green")
ax.plot(df.index, df["日收预期"], label="按预期收益计算的累积净值", 
        marker="^", linestyle="-.", color="red")
ax.plot(df.index, df["日收复合"], label="按复合收益计算的累积净值", 
        marker="*", linestyle=":", color="orange")


ax.set_title("四种累积净值对比", fontsize=15)
ax.set_xlabel("日期", fontsize=12)
ax.set_ylabel("累积净值", fontsize=12)
ax.grid(True, linestyle="--", alpha=0.7)
ax.legend(fontsize=10)

ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
plt.xticks(rotation=45)

print("10日收益数据及三种累积净值计算结果：")
pd.set_option("display.float_format", "{:.4f}".format)
df
```

我们将得到下图：

![](https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250722143145.png)


以及各种收益计算的净值结果：

![](https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250722143243.png)

-->