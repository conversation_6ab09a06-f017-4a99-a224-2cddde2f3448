# Slidev 主题修改模板指南

## 📋 项目概述
本文档提供了修改 Slidev 主题（特别是 portrait-flora 主题）的标准化流程和命令模板。

## 🎯 核心组件结构

### 1. 主题文件结构
```
themes/portrait-flora/
├── layouts/
│   └── cover.vue          # 封面页布局
├── components/
│   ├── ApiCardCover.vue   # API 函数介绍卡片
│   └── ApiCardBody.vue    # 示例代码卡片
├── setup/
│   └── main.ts           # 组件注册
└── styles/
    └── layouts.css       # 主题样式
```

### 2. 页面文件结构
```
pages/
└── qstats.md            # 内容页面
```

## 🔧 修改命令模板

### 一、封面页标签功能添加

#### 1.1 在 cover.vue 中添加标签支持
**目标**：在封面页右下角/标题下方显示标签

**修改位置**：`themes/portrait-flora/layouts/cover.vue`

**Script 部分添加**：
```javascript
// 获取 tags 配置
const tags = computed(() => {
    if ($slidev.configs.tags && Array.isArray($slidev.configs.tags)) {
        return $slidev.configs.tags
    }
    return []
})
```

**Template 部分添加**：
```html
<!-- Tags 显示 -->
<div v-if="tags && tags.length > 0" class="cover-tags">
    <span v-for="tag in tags" :key="tag" class="cover-tag">{{ tag }}</span>
</div>
```

**Style 部分添加**：
```css
/* Tags 样式配置 */
.cover-tags {
    position: absolute;
    top: 75%;                    /* 位置：标题下方 */
    left: 50%;                   /* 水平居中 */
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;      /* 竖向排列 */
    gap: 1rem;
    align-items: center;
    z-index: 10;
}

.cover-tag {
    background: rgba(231, 76, 60, 0.9);  /* 主题色 */
    color: white;
    padding: 1rem 2rem;                  /* 内边距 */
    border-radius: 1.5rem;               /* 圆角矩形 */
    font-size: 4vw;                      /* 字体大小 */
    font-weight: 600;
    min-width: 200px;                    /* 统一宽度 */
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
}

.cover-tag:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
}
```

#### 1.2 调整封面元素大小和位置
**标题位置调整**：
```css
.title {
    font-size: 12vw;           /* 字体大小 */
    /* 模板中使用 top-55% 类调整位置 */
}
```

**其他元素调整**：
```css
.seq {
    font-size: 4.5vw;          /* 序列号字体 */
    padding: 15px 40px;        /* 内边距 */
}

.motto {
    font-size: 4vw;            /* 座右铭字体 */
    padding: 15px 40px;        /* 内边距 */
}
```

### 二、API 卡片组件标签功能

#### 2.1 为 ApiCardCover 和 ApiCardBody 添加标签支持

**Script 部分修改**：
```javascript
const props = defineProps({
    // 现有属性...
    tags: {
        type: Array,
        default: () => []
    }
})

const tags = computed(() => {
    if (props.tags && props.tags.length > 0) {
        return props.tags
    }
    return []
})
```

**Template 部分添加**：
```html
<!-- 标签容器 -->
<div v-if="tags && tags.length > 0" class="tags-container">
    <span v-for="tag in tags" :key="tag" class="tag">{{ tag }}</span>
</div>
```

**Style 部分添加**：
```css
.qstats-card {
    position: relative;  /* 为标签定位做准备 */
}

.tags-container {
    position: absolute;
    bottom: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-end;
    max-width: 60%;
}

.tag {
    background: #e74c3c;        /* 与主题色一致 */
    color: white;
    padding: 4px 12px;
    border-radius: 12px;        /* 圆角矩形 */
    font-size: 2.2vw;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}
```

#### 2.2 组件注册
**修改位置**：`themes/portrait-flora/setup/main.ts`

```javascript
import { defineAppSetup } from '@slidev/types'
import ApiCardCover from '../components/ApiCardCover.vue'
import ApiCardBody from '../components/ApiCardBody.vue'

export default defineAppSetup(({ app, router }) => {
  app.component('ApiCardCover', ApiCardCover)
  app.component('ApiCardBody', ApiCardBody)
})
```

### 三、内容页面结构化

#### 3.1 Markdown 文件前置配置
```yaml
---
theme: themes/portrait-flora
title: 页面标题
seq: 序列信息
motto: 座右铭
tags: [tag1, tag2, tag3]
layout: cover
---
```

#### 3.2 API 卡片使用模板
```markdown
<ApiCardCover category="分类" title="函数名()" :tags="['tag1', 'tag2']">
  <template #description>
    函数描述
  </template>
  <template #signature>
    函数签名
  </template>
  <template #returns>
    返回值说明
  </template>
  <template #params>
• 参数1: 说明
• 参数2: 说明
  </template>
  <template #example>
```python
# 代码示例
```
  </template>
</ApiCardCover>
```

#### 3.3 长内容拆分模板
**第一张卡片**：
```markdown
<ApiCardBody title="示例标题" :tags="['tag1', 'tag2']">
  <template #example>
```python
# 第一部分代码
```
  </template>
</ApiCardBody>

---

续前一页。

<ApiCardBody title="示例标题 - 续" :tags="['tag3', 'tag4']">
  <template #example>
```python
# 第二部分代码
```
  </template>
  
  <template #output>
输出结果
  </template>
</ApiCardBody>
```

## 📝 最佳实践

### 1. 标签命名规范
- **功能标签**：`compsum()`, `comp()`, `expected_return()`
- **类型标签**：`quantstats`, `python`, `finance`
- **用途标签**：`example`, `demo`, `advanced`, `portfolio`

### 2. 内容拆分原则
- 单张卡片代码行数不超过 20-25 行
- 逻辑完整性：在自然断点处拆分
- 使用"续前一页"连接相关内容

### 3. 样式一致性
- 标签颜色与主题色保持一致
- 字体大小使用 vw 单位保证响应式
- 圆角矩形统一使用 1-1.5rem

### 4. 组件使用选择
- **ApiCardCover**：用于函数/API 介绍
- **ApiCardBody**：用于示例代码和说明

## 🚀 快速应用流程

1. **确定修改需求**：标签位置、样式、内容结构
2. **选择对应模板**：根据需求选择上述模板
3. **批量替换**：使用模板中的代码片段
4. **测试验证**：启动 Slidev 预览效果
5. **微调优化**：根据实际效果调整参数

## 📞 常用修改命令

### 位置调整
- `top-45%` → `top-55%`：标题下移
- `bottom: 1rem; right: 1rem`：右下角定位
- `left: 50%; transform: translateX(-50%)`：水平居中

### 大小调整
- `font-size: 12vw`：标题字体
- `font-size: 4vw`：标签字体
- `padding: 1rem 2rem`：标签内边距

### 布局调整
- `flex-direction: column`：竖向排列
- `flex-direction: row`：横向排列
- `gap: 1rem`：元素间距
