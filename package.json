{"name": "slidev", "type": "module", "private": true, "scripts": {"build": "slidev build", "dev": "slidev --open", "export": "slidev export"}, "dependencies": {"@iconify/vue": "^4.1.2", "@slidev/cli": "0.49.29", "@slidev/theme-default": "latest", "@slidev/theme-seriph": "latest", "animate.css": "^4.1.1", "crypto-js": "^4.2.0", "markdown-it-admon": "^1.0.1", "markdown-it-container": "^4.0.0", "markdown-it-emoji": "^3.0.0", "markmap-lib": "^0.17.0", "plotly.js-dist": "^2.35.2", "slidev-addon-python-runner": "^0.1.3", "thebe-core": "^0.4.10", "tinycolor2": "^1.6.0", "ttf2woff2": "^6.0.1", "vite": "5.3", "vue": "3.5.2", "vue3-carousel": "^0.3.3", "vuewordcloud": "^19.0.0", "yaml": "^2.4.2"}, "devDependencies": {"@iconify-json/game-icons": "^1.2.0", "@iconify-json/ic": "^1.2.0", "@iconify-json/ion": "^1.2.0", "@iconify-json/material-symbols": "^1.2.10", "@iconify-json/material-symbols-light": "^1.2.10", "@iconify-json/mdi": "^1.2.1", "@shikijs/transformers": "^1.16.3", "playwright-chromium": "^1.48.0"}}