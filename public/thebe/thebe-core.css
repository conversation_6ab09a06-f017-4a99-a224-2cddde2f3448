/* ../../node_modules/@jupyterlab/theme-light-extension/style/variables.css */
:root {
  --jp-shadow-base-lightness: 0;
  --jp-shadow-umbra-color: rgba( var(--jp-shadow-base-lightness), var(--jp-shadow-base-lightness), var(--jp-shadow-base-lightness), 0.2 );
  --jp-shadow-penumbra-color: rgba( var(--jp-shadow-base-lightness), var(--jp-shadow-base-lightness), var(--jp-shadow-base-lightness), 0.14 );
  --jp-shadow-ambient-color: rgba( var(--jp-shadow-base-lightness), var(--jp-shadow-base-lightness), var(--jp-shadow-base-lightness), 0.12 );
  --jp-elevation-z0: none;
  --jp-elevation-z1:
    0 2px 1px -1px var(--jp-shadow-umbra-color),
    0 1px 1px 0 var(--jp-shadow-penumbra-color),
    0 1px 3px 0 var(--jp-shadow-ambient-color);
  --jp-elevation-z2:
    0 3px 1px -2px var(--jp-shadow-umbra-color),
    0 2px 2px 0 var(--jp-shadow-penumbra-color),
    0 1px 5px 0 var(--jp-shadow-ambient-color);
  --jp-elevation-z4:
    0 2px 4px -1px var(--jp-shadow-umbra-color),
    0 4px 5px 0 var(--jp-shadow-penumbra-color),
    0 1px 10px 0 var(--jp-shadow-ambient-color);
  --jp-elevation-z6:
    0 3px 5px -1px var(--jp-shadow-umbra-color),
    0 6px 10px 0 var(--jp-shadow-penumbra-color),
    0 1px 18px 0 var(--jp-shadow-ambient-color);
  --jp-elevation-z8:
    0 5px 5px -3px var(--jp-shadow-umbra-color),
    0 8px 10px 1px var(--jp-shadow-penumbra-color),
    0 3px 14px 2px var(--jp-shadow-ambient-color);
  --jp-elevation-z12:
    0 7px 8px -4px var(--jp-shadow-umbra-color),
    0 12px 17px 2px var(--jp-shadow-penumbra-color),
    0 5px 22px 4px var(--jp-shadow-ambient-color);
  --jp-elevation-z16:
    0 8px 10px -5px var(--jp-shadow-umbra-color),
    0 16px 24px 2px var(--jp-shadow-penumbra-color),
    0 6px 30px 5px var(--jp-shadow-ambient-color);
  --jp-elevation-z20:
    0 10px 13px -6px var(--jp-shadow-umbra-color),
    0 20px 31px 3px var(--jp-shadow-penumbra-color),
    0 8px 38px 7px var(--jp-shadow-ambient-color);
  --jp-elevation-z24:
    0 11px 15px -7px var(--jp-shadow-umbra-color),
    0 24px 38px 3px var(--jp-shadow-penumbra-color),
    0 9px 46px 8px var(--jp-shadow-ambient-color);
  --jp-border-width: 1px;
  --jp-border-color0: var(--md-grey-400, #bdbdbd);
  --jp-border-color1: var(--md-grey-400, #bdbdbd);
  --jp-border-color2: var(--md-grey-300, #e0e0e0);
  --jp-border-color3: var(--md-grey-200, #eee);
  --jp-inverse-border-color: var(--md-grey-600, #757575);
  --jp-border-radius: 2px;
  --jp-ui-font-scale-factor: 1.2;
  --jp-ui-font-size0: 0.83333em;
  --jp-ui-font-size1: 13px;
  --jp-ui-font-size2: 1.2em;
  --jp-ui-font-size3: 1.44em;
  --jp-ui-font-family:
    system-ui,
    -apple-system,
    blinkmacsystemfont,
    "Segoe UI",
    helvetica,
    arial,
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol";
  --jp-ui-font-color0: rgba(0, 0, 0, 1);
  --jp-ui-font-color1: rgba(0, 0, 0, 0.87);
  --jp-ui-font-color2: rgba(0, 0, 0, 0.54);
  --jp-ui-font-color3: rgba(0, 0, 0, 0.38);
  --jp-ui-inverse-font-color0: rgba(255, 255, 255, 1);
  --jp-ui-inverse-font-color1: rgba(255, 255, 255, 1);
  --jp-ui-inverse-font-color2: rgba(255, 255, 255, 0.7);
  --jp-ui-inverse-font-color3: rgba(255, 255, 255, 0.5);
  --jp-content-line-height: 1.6;
  --jp-content-font-scale-factor: 1.2;
  --jp-content-font-size0: 0.83333em;
  --jp-content-font-size1: 14px;
  --jp-content-font-size2: 1.2em;
  --jp-content-font-size3: 1.44em;
  --jp-content-font-size4: 1.728em;
  --jp-content-font-size5: 2.0736em;
  --jp-content-presentation-font-size1: 17px;
  --jp-content-heading-line-height: 1;
  --jp-content-heading-margin-top: 1.2em;
  --jp-content-heading-margin-bottom: 0.8em;
  --jp-content-heading-font-weight: 500;
  --jp-content-font-color0: rgba(0, 0, 0, 1);
  --jp-content-font-color1: rgba(0, 0, 0, 0.87);
  --jp-content-font-color2: rgba(0, 0, 0, 0.54);
  --jp-content-font-color3: rgba(0, 0, 0, 0.38);
  --jp-content-link-color: var(--md-blue-900, #0d47a1);
  --jp-content-link-visited-color: var(--md-purple-700, #7b1fa2);
  --jp-content-font-family:
    system-ui,
    -apple-system,
    blinkmacsystemfont,
    "Segoe UI",
    helvetica,
    arial,
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol";
  --jp-code-font-size: 13px;
  --jp-code-line-height: 1.3077;
  --jp-code-padding: 5px;
  --jp-code-font-family-default:
    menlo,
    consolas,
    "DejaVu Sans Mono",
    monospace;
  --jp-code-font-family: var(--jp-code-font-family-default);
  --jp-code-presentation-font-size: 16px;
  --jp-code-cursor-width0: 1.4px;
  --jp-code-cursor-width1: 2px;
  --jp-code-cursor-width2: 4px;
  --jp-layout-color0: white;
  --jp-layout-color1: white;
  --jp-layout-color2: var(--md-grey-200, #eee);
  --jp-layout-color3: var(--md-grey-400, #bdbdbd);
  --jp-layout-color4: var(--md-grey-600, #757575);
  --jp-inverse-layout-color0: #111;
  --jp-inverse-layout-color1: var(--md-grey-900, #212121);
  --jp-inverse-layout-color2: var(--md-grey-800, #424242);
  --jp-inverse-layout-color3: var(--md-grey-700, #616161);
  --jp-inverse-layout-color4: var(--md-grey-600, #757575);
  --jp-brand-color0: var(--md-blue-900, #0d47a1);
  --jp-brand-color1: var(--md-blue-700, #1976d2);
  --jp-brand-color2: var(--md-blue-300, #64b5f6);
  --jp-brand-color3: var(--md-blue-100, #bbdefb);
  --jp-brand-color4: var(--md-blue-50, #e3f2fd);
  --jp-accent-color0: var(--md-green-900, #1b5e20);
  --jp-accent-color1: var(--md-green-700, #388e3c);
  --jp-accent-color2: var(--md-green-300, #81c784);
  --jp-accent-color3: var(--md-green-100, #c8e6c9);
  --jp-warn-color0: var(--md-orange-900, #e65100);
  --jp-warn-color1: var(--md-orange-700, #f57c00);
  --jp-warn-color2: var(--md-orange-300, #ffb74d);
  --jp-warn-color3: var(--md-orange-100, #ffe0b2);
  --jp-error-color0: var(--md-red-900, #b71c1c);
  --jp-error-color1: var(--md-red-700, #d32f2f);
  --jp-error-color2: var(--md-red-300, #e57373);
  --jp-error-color3: var(--md-red-100, #ffcdd2);
  --jp-success-color0: var(--md-green-900, #1b5e20);
  --jp-success-color1: var(--md-green-700, #388e3c);
  --jp-success-color2: var(--md-green-300, #81c784);
  --jp-success-color3: var(--md-green-100, #c8e6c9);
  --jp-info-color0: var(--md-cyan-900, #006064);
  --jp-info-color1: var(--md-cyan-700, #0097a7);
  --jp-info-color2: var(--md-cyan-300, #4dd0e1);
  --jp-info-color3: var(--md-cyan-100, #b2ebf2);
  --jp-cell-padding: 5px;
  --jp-cell-collapser-width: 8px;
  --jp-cell-collapser-min-height: 20px;
  --jp-cell-collapser-not-active-hover-opacity: 0.6;
  --jp-cell-editor-background: var(--md-grey-100, #f5f5f5);
  --jp-cell-editor-border-color: var(--md-grey-300, #e0e0e0);
  --jp-cell-editor-box-shadow: inset 0 0 2px var(--md-blue-300, #64b5f6);
  --jp-cell-editor-active-background: var(--jp-layout-color0);
  --jp-cell-editor-active-border-color: var(--jp-brand-color1);
  --jp-cell-prompt-width: 64px;
  --jp-cell-prompt-font-family: var(--jp-code-font-family-default);
  --jp-cell-prompt-letter-spacing: 0;
  --jp-cell-prompt-opacity: 1;
  --jp-cell-prompt-not-active-opacity: 0.5;
  --jp-cell-prompt-not-active-font-color: var(--md-grey-700, #616161);
  --jp-cell-inprompt-font-color: #307fc1;
  --jp-cell-outprompt-font-color: #bf5b3d;
  --jp-notebook-padding: 10px;
  --jp-notebook-select-background: var(--jp-layout-color1);
  --jp-notebook-multiselected-color: var(--md-blue-50, #e3f2fd);
  --jp-notebook-scroll-padding: calc( 100% - var(--jp-code-font-size) * var(--jp-code-line-height) - var(--jp-code-padding) - var(--jp-cell-padding) - 1px );
  --jp-rendermime-error-background: #fdd;
  --jp-rendermime-table-row-background: var(--md-grey-100, #cfd8dc);
  --jp-rendermime-table-row-hover-background: var(--md-light-blue-50, #e1f5fe);
  --jp-dialog-background: rgba(0, 0, 0, 0.25);
  --jp-console-padding: 10px;
  --jp-toolbar-border-color: var(--jp-border-color1);
  --jp-toolbar-micro-height: 8px;
  --jp-toolbar-background: var(--jp-layout-color1);
  --jp-toolbar-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.24);
  --jp-toolbar-header-margin: 4px 4px 0 4px;
  --jp-toolbar-active-background: var(--md-grey-300, #90a4ae);
  --jp-statusbar-height: 24px;
  --jp-input-box-shadow: inset 0 0 2px var(--md-blue-300, #64b5f6);
  --jp-input-active-background: var(--jp-layout-color1);
  --jp-input-hover-background: var(--jp-layout-color1);
  --jp-input-background: var(--md-grey-100, #f5f5f5);
  --jp-input-border-color: var(--jp-inverse-border-color);
  --jp-input-active-border-color: var(--jp-brand-color1);
  --jp-input-active-box-shadow-color: rgba(19, 124, 189, 0.3);
  --jp-editor-selected-background: #d9d9d9;
  --jp-editor-selected-focused-background: #d7d4f0;
  --jp-editor-cursor-color: var(--jp-ui-font-color0);
  --jp-mirror-editor-keyword-color: #008000;
  --jp-mirror-editor-atom-color: #88f;
  --jp-mirror-editor-number-color: #080;
  --jp-mirror-editor-def-color: #00f;
  --jp-mirror-editor-variable-color: var(--md-grey-900, #212121);
  --jp-mirror-editor-variable-2-color: rgb(0, 54, 109);
  --jp-mirror-editor-variable-3-color: #085;
  --jp-mirror-editor-punctuation-color: #05a;
  --jp-mirror-editor-property-color: #05a;
  --jp-mirror-editor-operator-color: #a2f;
  --jp-mirror-editor-comment-color: #408080;
  --jp-mirror-editor-string-color: #ba2121;
  --jp-mirror-editor-string-2-color: #708;
  --jp-mirror-editor-meta-color: #a2f;
  --jp-mirror-editor-qualifier-color: #555;
  --jp-mirror-editor-builtin-color: #008000;
  --jp-mirror-editor-bracket-color: #997;
  --jp-mirror-editor-tag-color: #170;
  --jp-mirror-editor-attribute-color: #00c;
  --jp-mirror-editor-header-color: blue;
  --jp-mirror-editor-quote-color: #090;
  --jp-mirror-editor-link-color: #00c;
  --jp-mirror-editor-error-color: #f00;
  --jp-mirror-editor-hr-color: #999;
  --jp-collaborator-color1: #ffad8e;
  --jp-collaborator-color2: #dac83d;
  --jp-collaborator-color3: #72dd76;
  --jp-collaborator-color4: #00e4d0;
  --jp-collaborator-color5: #45d4ff;
  --jp-collaborator-color6: #e2b1ff;
  --jp-collaborator-color7: #ff9de6;
  --jp-vega-background: white;
  --jp-sidebar-min-width: 250px;
  --jp-search-toggle-off-opacity: 0.5;
  --jp-search-toggle-hover-opacity: 0.8;
  --jp-search-toggle-on-opacity: 1;
  --jp-search-selected-match-background-color: rgb(245, 200, 0);
  --jp-search-selected-match-color: black;
  --jp-search-unselected-match-background-color: var( --jp-inverse-layout-color0 );
  --jp-search-unselected-match-color: var(--jp-ui-inverse-font-color0);
  --jp-icon-contrast-color0: var(--md-purple-600, #8e24aa);
  --jp-icon-contrast-color1: var(--md-green-600, #43a047);
  --jp-icon-contrast-color2: var(--md-pink-600, #d81b60);
  --jp-icon-contrast-color3: var(--md-blue-600, #1e88e5);
  --jp-accept-color-normal: var(--md-blue-700, #1976d2);
  --jp-accept-color-hover: var(--md-blue-800, #1565c0);
  --jp-accept-color-active: var(--md-blue-900, #0d47a1);
  --jp-warn-color-normal: var(--md-red-700, #d32f2f);
  --jp-warn-color-hover: var(--md-red-800, #c62828);
  --jp-warn-color-active: var(--md-red-900, #b71c1c);
  --jp-reject-color-normal: var(--md-grey-600, #757575);
  --jp-reject-color-hover: var(--md-grey-700, #616161);
  --jp-reject-color-active: var(--md-grey-800, #424242);
  --jp-jupyter-icon-color: #f37626;
  --jp-notebook-icon-color: #f37626;
  --jp-json-icon-color: var(--md-orange-700, #f57c00);
  --jp-console-icon-background-color: var(--md-blue-700, #1976d2);
  --jp-console-icon-color: white;
  --jp-terminal-icon-background-color: var(--md-grey-800, #424242);
  --jp-terminal-icon-color: var(--md-grey-200, #eee);
  --jp-text-editor-icon-color: var(--md-grey-700, #616161);
  --jp-inspector-icon-color: var(--md-grey-700, #616161);
  --jp-switch-color: var(--md-grey-400, #bdbdbd);
  --jp-switch-true-position-color: var(--md-orange-900, #e65100);
}

/* ../../node_modules/@jupyterlab/theme-light-extension/style/theme.css */
tt,
code,
kbd,
samp,
pre {
  font-family: var(--jp-code-font-family);
  font-size: var(--jp-code-font-size);
  line-height: var(--jp-code-line-height);
}

/* ../../node_modules/@jupyter-widgets/controls/css/lumino.css */
.jupyter-widgets.widget-tab > .p-TabBar,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar {
  display: flex;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.jupyter-widgets.widget-tab > .p-TabBar[data-orientation=horizontal],
.jupyter-widgets.jupyter-widget-tab > .p-TabBar[data-orientation=horizontal],
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar[data-orientation=horizontal] {
  flex-direction: row;
}
.jupyter-widgets.widget-tab > .p-TabBar[data-orientation=vertical],
.jupyter-widgets.jupyter-widget-tab > .p-TabBar[data-orientation=vertical],
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar[data-orientation=vertical] {
  flex-direction: column;
}
.jupyter-widgets.widget-tab > .p-TabBar > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar > .lm-TabBar-content {
  margin: 0;
  padding: 0;
  display: flex;
  flex: 1 1 auto;
  list-style-type: none;
}
.jupyter-widgets.widget-tab > .p-TabBar[data-orientation=horizontal] > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar[data-orientation=horizontal] > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar[data-orientation=horizontal] > .lm-TabBar-content {
  flex-direction: row;
}
.jupyter-widgets.widget-tab > .p-TabBar[data-orientation=vertical] > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar[data-orientation=vertical] > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar[data-orientation=vertical] > .lm-TabBar-content {
  flex-direction: column;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  overflow: hidden;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tabIcon,
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tabCloseIcon,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tabIcon,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tabCloseIcon,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tabIcon,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tabCloseIcon {
  flex: 0 0 auto;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tabLabel,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tabLabel,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tabLabel {
  flex: 1 1 auto;
  overflow: hidden;
  white-space: nowrap;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab.p-mod-hidden,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab.p-mod-hidden,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab.lm-mod-hidden {
  display: none !important;
}
.jupyter-widgets.widget-tab > .p-TabBar.p-mod-dragging .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar.p-mod-dragging .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar.lm-mod-dragging .lm-TabBar-tab {
  position: relative;
}
.jupyter-widgets.widget-tab > .p-TabBar.p-mod-dragging[data-orientation=horizontal] .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar.p-mod-dragging[data-orientation=horizontal] .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar.lm-mod-dragging[data-orientation=horizontal] .lm-TabBar-tab {
  left: 0;
  transition: left 150ms ease;
}
.jupyter-widgets.widget-tab > .p-TabBar.p-mod-dragging[data-orientation=vertical] .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar.p-mod-dragging[data-orientation=vertical] .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar.lm-mod-dragging[data-orientation=vertical] .lm-TabBar-tab {
  top: 0;
  transition: top 150ms ease;
}
.jupyter-widgets.widget-tab > .p-TabBar.p-mod-dragging .p-TabBar-tab.p-mod-dragging,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar.p-mod-dragging .p-TabBar-tab.p-mod-dragging,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar.lm-mod-dragging .lm-TabBar-tab.lm-mod-dragging {
  transition: none;
}

/* ../../node_modules/@jupyter-widgets/controls/css/nouislider.css */
.widget-slider,
.jupyter-widget-slider {
}
.widget-slider .noUi-target,
.jupyter-widget-slider .noUi-target,
.widget-slider .noUi-target *,
.jupyter-widget-slider .noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.widget-slider .noUi-target,
.jupyter-widget-slider .noUi-target {
  position: relative;
}
.widget-slider .noUi-base,
.jupyter-widget-slider .noUi-base,
.widget-slider .noUi-connects,
.jupyter-widget-slider .noUi-connects {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}
.widget-slider .noUi-connects,
.jupyter-widget-slider .noUi-connects {
  overflow: hidden;
  z-index: 0;
}
.widget-slider .noUi-connect,
.jupyter-widget-slider .noUi-connect,
.widget-slider .noUi-origin,
.jupyter-widget-slider .noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  -webkit-transform-style: preserve-3d;
  transform-origin: 0 0;
  transform-style: flat;
}
.widget-slider .noUi-connect,
.jupyter-widget-slider .noUi-connect {
  height: 100%;
  width: 100%;
}
.widget-slider .noUi-origin,
.jupyter-widget-slider .noUi-origin {
  height: 10%;
  width: 10%;
}
.widget-slider .noUi-txt-dir-rtl.noUi-horizontal .noUi-origin,
.jupyter-widget-slider .noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  left: 0;
  right: auto;
}
.widget-slider .noUi-vertical .noUi-origin,
.jupyter-widget-slider .noUi-vertical .noUi-origin {
  width: 0;
}
.widget-slider .noUi-horizontal .noUi-origin,
.jupyter-widget-slider .noUi-horizontal .noUi-origin {
  height: 0;
}
.widget-slider .noUi-handle,
.jupyter-widget-slider .noUi-handle {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
}
.widget-slider .noUi-touch-area,
.jupyter-widget-slider .noUi-touch-area {
  height: 100%;
  width: 100%;
}
.widget-slider .noUi-state-tap .noUi-connect,
.jupyter-widget-slider .noUi-state-tap .noUi-connect,
.widget-slider .noUi-state-tap .noUi-origin,
.jupyter-widget-slider .noUi-state-tap .noUi-origin {
  -webkit-transition: transform 0.3s;
  transition: transform 0.3s;
}
.widget-slider .noUi-state-drag *,
.jupyter-widget-slider .noUi-state-drag * {
  cursor: inherit !important;
}
.widget-slider .noUi-horizontal,
.jupyter-widget-slider .noUi-horizontal {
  height: 18px;
}
.widget-slider .noUi-horizontal .noUi-handle,
.jupyter-widget-slider .noUi-horizontal .noUi-handle {
  width: 34px;
  height: 28px;
  right: -17px;
  top: -6px;
}
.widget-slider .noUi-vertical,
.jupyter-widget-slider .noUi-vertical {
  width: 18px;
}
.widget-slider .noUi-vertical .noUi-handle,
.jupyter-widget-slider .noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  right: -6px;
  top: -17px;
}
.widget-slider .noUi-txt-dir-rtl.noUi-horizontal .noUi-handle,
.jupyter-widget-slider .noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -17px;
  right: auto;
}
.widget-slider .noUi-target,
.jupyter-widget-slider .noUi-target {
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #D3D3D3;
  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;
}
.widget-slider .noUi-connects,
.jupyter-widget-slider .noUi-connects {
  border-radius: 3px;
}
.widget-slider .noUi-connect,
.jupyter-widget-slider .noUi-connect {
  background: #3FB8AF;
}
.widget-slider .noUi-draggable,
.jupyter-widget-slider .noUi-draggable {
  cursor: ew-resize;
}
.widget-slider .noUi-vertical .noUi-draggable,
.jupyter-widget-slider .noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}
.widget-slider .noUi-handle,
.jupyter-widget-slider .noUi-handle {
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #FFF;
  cursor: default;
  box-shadow:
    inset 0 0 1px #FFF,
    inset 0 1px 7px #EBEBEB,
    0 3px 6px -3px #BBB;
}
.widget-slider .noUi-active,
.jupyter-widget-slider .noUi-active {
  box-shadow:
    inset 0 0 1px #FFF,
    inset 0 1px 7px #DDD,
    0 3px 6px -3px #BBB;
}
.widget-slider .noUi-handle:before,
.jupyter-widget-slider .noUi-handle:before,
.widget-slider .noUi-handle:after,
.jupyter-widget-slider .noUi-handle:after {
  content: "";
  display: block;
  position: absolute;
  height: 14px;
  width: 1px;
  background: #E8E7E6;
  left: 14px;
  top: 6px;
}
.widget-slider .noUi-handle:after,
.jupyter-widget-slider .noUi-handle:after {
  left: 17px;
}
.widget-slider .noUi-vertical .noUi-handle:before,
.jupyter-widget-slider .noUi-vertical .noUi-handle:before,
.widget-slider .noUi-vertical .noUi-handle:after,
.jupyter-widget-slider .noUi-vertical .noUi-handle:after {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px;
}
.widget-slider .noUi-vertical .noUi-handle:after,
.jupyter-widget-slider .noUi-vertical .noUi-handle:after {
  top: 17px;
}
.widget-slider [disabled] .noUi-connect,
.jupyter-widget-slider [disabled] .noUi-connect {
  background: #B8B8B8;
}
.widget-slider [disabled].noUi-target,
.jupyter-widget-slider [disabled].noUi-target,
.widget-slider [disabled].noUi-handle,
.jupyter-widget-slider [disabled].noUi-handle,
.widget-slider [disabled] .noUi-handle,
.jupyter-widget-slider [disabled] .noUi-handle {
  cursor: not-allowed;
}
.widget-slider .noUi-pips,
.jupyter-widget-slider .noUi-pips,
.widget-slider .noUi-pips *,
.jupyter-widget-slider .noUi-pips * {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.widget-slider .noUi-pips,
.jupyter-widget-slider .noUi-pips {
  position: absolute;
  color: #999;
}
.widget-slider .noUi-value,
.jupyter-widget-slider .noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center;
}
.widget-slider .noUi-value-sub,
.jupyter-widget-slider .noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}
.widget-slider .noUi-marker,
.jupyter-widget-slider .noUi-marker {
  position: absolute;
  background: #CCC;
}
.widget-slider .noUi-marker-sub,
.jupyter-widget-slider .noUi-marker-sub {
  background: #AAA;
}
.widget-slider .noUi-marker-large,
.jupyter-widget-slider .noUi-marker-large {
  background: #AAA;
}
.widget-slider .noUi-pips-horizontal,
.jupyter-widget-slider .noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%;
}
.widget-slider .noUi-value-horizontal,
.jupyter-widget-slider .noUi-value-horizontal {
  -webkit-transform: translate(-50%, 50%);
  transform: translate(-50%, 50%);
}
.noUi-rtl .widget-slider .noUi-value-horizontal,
.noUi-rtl .jupyter-widget-slider .noUi-value-horizontal {
  -webkit-transform: translate(50%, 50%);
  transform: translate(50%, 50%);
}
.widget-slider .noUi-marker-horizontal.noUi-marker,
.jupyter-widget-slider .noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px;
}
.widget-slider .noUi-marker-horizontal.noUi-marker-sub,
.jupyter-widget-slider .noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}
.widget-slider .noUi-marker-horizontal.noUi-marker-large,
.jupyter-widget-slider .noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}
.widget-slider .noUi-pips-vertical,
.jupyter-widget-slider .noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%;
}
.widget-slider .noUi-value-vertical,
.jupyter-widget-slider .noUi-value-vertical {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  padding-left: 25px;
}
.noUi-rtl .widget-slider .noUi-value-vertical,
.noUi-rtl .jupyter-widget-slider .noUi-value-vertical {
  -webkit-transform: translate(0, 50%);
  transform: translate(0, 50%);
}
.widget-slider .noUi-marker-vertical.noUi-marker,
.jupyter-widget-slider .noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px;
}
.widget-slider .noUi-marker-vertical.noUi-marker-sub,
.jupyter-widget-slider .noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}
.widget-slider .noUi-marker-vertical.noUi-marker-large,
.jupyter-widget-slider .noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}
.widget-slider .noUi-tooltip,
.jupyter-widget-slider .noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
  white-space: nowrap;
}
.widget-slider .noUi-horizontal .noUi-tooltip,
.jupyter-widget-slider .noUi-horizontal .noUi-tooltip {
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%;
}
.widget-slider .noUi-vertical .noUi-tooltip,
.jupyter-widget-slider .noUi-vertical .noUi-tooltip {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  top: 50%;
  right: 120%;
}
.widget-slider .noUi-horizontal .noUi-origin > .noUi-tooltip,
.jupyter-widget-slider .noUi-horizontal .noUi-origin > .noUi-tooltip {
  -webkit-transform: translate(50%, 0);
  transform: translate(50%, 0);
  left: auto;
  bottom: 10px;
}
.widget-slider .noUi-vertical .noUi-origin > .noUi-tooltip,
.jupyter-widget-slider .noUi-vertical .noUi-origin > .noUi-tooltip {
  -webkit-transform: translate(0, -18px);
  transform: translate(0, -18px);
  top: auto;
  right: 28px;
}
.widget-slider .noUi-connect,
.jupyter-widget-slider .noUi-connect {
  background: #2196f3;
}
.widget-slider .noUi-horizontal,
.jupyter-widget-slider .noUi-horizontal {
  height: var(--jp-widgets-slider-track-thickness);
}
.widget-slider .noUi-vertical,
.jupyter-widget-slider .noUi-vertical {
  width: var(--jp-widgets-slider-track-thickness);
  height: 100%;
}
.widget-slider .noUi-horizontal .noUi-handle,
.jupyter-widget-slider .noUi-horizontal .noUi-handle {
  width: var(--jp-widgets-slider-handle-size);
  height: var(--jp-widgets-slider-handle-size);
  border-radius: 50%;
  top: calc((var(--jp-widgets-slider-track-thickness) - var(--jp-widgets-slider-handle-size)) / 2);
  right: calc(var(--jp-widgets-slider-handle-size) / -2);
}
.widget-slider .noUi-vertical .noUi-handle,
.jupyter-widget-slider .noUi-vertical .noUi-handle {
  height: var(--jp-widgets-slider-handle-size);
  width: var(--jp-widgets-slider-handle-size);
  border-radius: 50%;
  right: calc((var(--jp-widgets-slider-handle-size) - var(--jp-widgets-slider-track-thickness)) / -2);
  top: calc(var(--jp-widgets-slider-handle-size) / -2);
}
.widget-slider .noUi-handle:after,
.jupyter-widget-slider .noUi-handle:after {
  content: none;
}
.widget-slider .noUi-handle:before,
.jupyter-widget-slider .noUi-handle:before {
  content: none;
}
.widget-slider .noUi-target,
.jupyter-widget-slider .noUi-target {
  background: #fafafa;
  border-radius: 4px;
  border: 1px;
}
.widget-slider .ui-slider,
.jupyter-widget-slider .ui-slider {
  border: var(--jp-widgets-slider-border-width) solid var(--jp-layout-color3);
  background: var(--jp-layout-color3);
  box-sizing: border-box;
  position: relative;
  border-radius: 0px;
}
.widget-slider .noUi-handle,
.jupyter-widget-slider .noUi-handle {
  width: var(--jp-widgets-slider-handle-size);
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  background: #fff;
  cursor: default;
  box-shadow: none;
  outline: none;
}
.widget-slider .noUi-target:not([disabled]) .noUi-handle:hover,
.jupyter-widget-slider .noUi-target:not([disabled]) .noUi-handle:hover,
.widget-slider .noUi-target:not([disabled]) .noUi-handle:focus,
.jupyter-widget-slider .noUi-target:not([disabled]) .noUi-handle:focus {
  background-color: var(--jp-widgets-slider-active-handle-color);
  border: var(--jp-widgets-slider-border-width) solid var(--jp-widgets-slider-active-handle-color);
}
.widget-slider [disabled].noUi-target,
.jupyter-widget-slider [disabled].noUi-target {
  opacity: 0.35;
}
.widget-slider .noUi-connects,
.jupyter-widget-slider .noUi-connects {
  overflow: visible;
  z-index: 0;
  background: var(--jp-layout-color3);
}
.widget-slider .noUi-vertical .noUi-connect,
.jupyter-widget-slider .noUi-vertical .noUi-connect {
  width: calc(100% + 2px);
  right: -1px;
}
.widget-slider .noUi-horizontal .noUi-connect,
.jupyter-widget-slider .noUi-horizontal .noUi-connect {
  height: calc(100% + 2px);
  top: -1px;
}

/* ../../node_modules/@jupyter-widgets/controls/css/widgets-base.css */
:root {
  --jp-widgets-color: var(--jp-content-font-color1);
  --jp-widgets-label-color: var(--jp-widgets-color);
  --jp-widgets-readout-color: var(--jp-widgets-color);
  --jp-widgets-font-size: var(--jp-ui-font-size1);
  --jp-widgets-margin: 2px;
  --jp-widgets-inline-height: 28px;
  --jp-widgets-inline-width: 300px;
  --jp-widgets-inline-width-short: calc( var(--jp-widgets-inline-width) / 2 - var(--jp-widgets-margin) );
  --jp-widgets-inline-width-tiny: calc( var(--jp-widgets-inline-width-short) / 2 - var(--jp-widgets-margin) );
  --jp-widgets-inline-margin: 4px;
  --jp-widgets-inline-label-width: 80px;
  --jp-widgets-border-width: var(--jp-border-width);
  --jp-widgets-vertical-height: 200px;
  --jp-widgets-horizontal-tab-height: 24px;
  --jp-widgets-horizontal-tab-width: 144px;
  --jp-widgets-horizontal-tab-top-border: 2px;
  --jp-widgets-progress-thickness: 20px;
  --jp-widgets-container-padding: 15px;
  --jp-widgets-input-padding: 4px;
  --jp-widgets-radio-item-height-adjustment: 8px;
  --jp-widgets-radio-item-height: calc( var(--jp-widgets-inline-height) - var(--jp-widgets-radio-item-height-adjustment) );
  --jp-widgets-slider-track-thickness: 4px;
  --jp-widgets-slider-border-width: var(--jp-widgets-border-width);
  --jp-widgets-slider-handle-size: 16px;
  --jp-widgets-slider-handle-border-color: var(--jp-border-color1);
  --jp-widgets-slider-handle-background-color: var(--jp-layout-color1);
  --jp-widgets-slider-active-handle-color: var(--jp-brand-color1);
  --jp-widgets-menu-item-height: 24px;
  --jp-widgets-dropdown-arrow: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjIuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxOCAxOCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTggMTg7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDpub25lO30KPC9zdHlsZT4KPHBhdGggZD0iTTUuMiw1LjlMOSw5LjdsMy44LTMuOGwxLjIsMS4ybC00LjksNWwtNC45LTVMNS4yLDUuOXoiLz4KPHBhdGggY2xhc3M9InN0MCIgZD0iTTAtMC42aDE4djE4SDBWLTAuNnoiLz4KPC9zdmc+Cg);
  --jp-widgets-input-color: var(--jp-ui-font-color1);
  --jp-widgets-input-background-color: var(--jp-layout-color1);
  --jp-widgets-input-border-color: var(--jp-border-color1);
  --jp-widgets-input-focus-border-color: var(--jp-brand-color2);
  --jp-widgets-input-border-width: var(--jp-widgets-border-width);
  --jp-widgets-disabled-opacity: 0.6;
  --md-shadow-key-umbra-opacity: 0.2;
  --md-shadow-key-penumbra-opacity: 0.14;
  --md-shadow-ambient-shadow-opacity: 0.12;
}
.jupyter-widgets {
  margin: var(--jp-widgets-margin);
  box-sizing: border-box;
  color: var(--jp-widgets-color);
  overflow: visible;
}
.jp-Output-result > .jupyter-widgets {
  margin-left: 0;
  margin-right: 0;
}
.widget-inline-hbox,
.jupyter-widget-inline-hbox {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: baseline;
}
.widget-inline-vbox,
.jupyter-widget-inline-vbox {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.widget-box,
.jupyter-widget-box {
  box-sizing: border-box;
  display: flex;
  margin: 0;
  overflow: auto;
}
.widget-gridbox,
.jupyter-widget-gridbox {
  box-sizing: border-box;
  display: grid;
  margin: 0;
  overflow: auto;
}
.widget-hbox,
.jupyter-widget-hbox {
  flex-direction: row;
}
.widget-vbox,
.jupyter-widget-vbox {
  flex-direction: column;
}
.jupyter-widget-tagsinput {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  overflow: auto;
  cursor: text;
}
.jupyter-widget-tag {
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  font-size: var(--jp-widgets-font-size);
  height: calc(var(--jp-widgets-inline-height) - 2px);
  border: 0px solid;
  line-height: calc(var(--jp-widgets-inline-height) - 2px);
  box-shadow: none;
  color: var(--jp-ui-font-color1);
  background-color: var(--jp-layout-color2);
  border-color: var(--jp-border-color2);
  border: none;
  user-select: none;
  cursor: grab;
  transition: margin-left 200ms;
  margin: 1px 1px 1px 1px;
}
.jupyter-widget-tag.mod-active {
  box-shadow:
    0 4px 5px 0 rgba(0, 0, 0, var(--md-shadow-key-penumbra-opacity)),
    0 1px 10px 0 rgba(0, 0, 0, var(--md-shadow-ambient-shadow-opacity)),
    0 2px 4px -1px rgba(0, 0, 0, var(--md-shadow-key-umbra-opacity));
  color: var(--jp-ui-font-color1);
  background-color: var(--jp-layout-color3);
}
.jupyter-widget-colortag {
  color: var(--jp-inverse-ui-font-color1);
}
.jupyter-widget-colortag.mod-active {
  color: var(--jp-inverse-ui-font-color0);
}
.jupyter-widget-taginput {
  color: var(--jp-ui-font-color0);
  background-color: var(--jp-layout-color0);
  cursor: text;
  text-align: left;
}
.jupyter-widget-taginput:focus {
  outline: none;
}
.jupyter-widget-tag-close {
  margin-left: var(--jp-widgets-inline-margin);
  padding: 2px 0px 2px 2px;
}
.jupyter-widget-tag-close:hover {
  cursor: pointer;
}
.jupyter-widget-tag.mod-primary {
  color: var(--jp-inverse-ui-font-color1);
  background-color: var(--jp-brand-color1);
}
.jupyter-widget-tag.mod-primary.mod-active {
  color: var(--jp-inverse-ui-font-color0);
  background-color: var(--jp-brand-color0);
}
.jupyter-widget-tag.mod-success {
  color: var(--jp-inverse-ui-font-color1);
  background-color: var(--jp-success-color1);
}
.jupyter-widget-tag.mod-success.mod-active {
  color: var(--jp-inverse-ui-font-color0);
  background-color: var(--jp-success-color0);
}
.jupyter-widget-tag.mod-info {
  color: var(--jp-inverse-ui-font-color1);
  background-color: var(--jp-info-color1);
}
.jupyter-widget-tag.mod-info.mod-active {
  color: var(--jp-inverse-ui-font-color0);
  background-color: var(--jp-info-color0);
}
.jupyter-widget-tag.mod-warning {
  color: var(--jp-inverse-ui-font-color1);
  background-color: var(--jp-warn-color1);
}
.jupyter-widget-tag.mod-warning.mod-active {
  color: var(--jp-inverse-ui-font-color0);
  background-color: var(--jp-warn-color0);
}
.jupyter-widget-tag.mod-danger {
  color: var(--jp-inverse-ui-font-color1);
  background-color: var(--jp-error-color1);
}
.jupyter-widget-tag.mod-danger.mod-active {
  color: var(--jp-inverse-ui-font-color0);
  background-color: var(--jp-error-color0);
}
.jupyter-button {
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  font-size: var(--jp-widgets-font-size);
  cursor: pointer;
  height: var(--jp-widgets-inline-height);
  border: 0px solid;
  line-height: var(--jp-widgets-inline-height);
  box-shadow: none;
  color: var(--jp-ui-font-color1);
  background-color: var(--jp-layout-color2);
  border-color: var(--jp-border-color2);
  border: none;
  user-select: none;
}
.jupyter-button i.fa {
  margin-right: var(--jp-widgets-inline-margin);
  pointer-events: none;
}
.jupyter-button:empty:before {
  content: "\200b";
}
.jupyter-widgets.jupyter-button:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.jupyter-button i.fa.center {
  margin-right: 0;
}
.jupyter-button:hover:enabled,
.jupyter-button:focus:enabled {
  box-shadow:
    0 2px 2px 0 rgba(0, 0, 0, var(--md-shadow-key-penumbra-opacity)),
    0 3px 1px -2px rgba(0, 0, 0, var(--md-shadow-key-umbra-opacity)),
    0 1px 5px 0 rgba(0, 0, 0, var(--md-shadow-ambient-shadow-opacity));
}
.jupyter-button:active,
.jupyter-button.mod-active {
  box-shadow:
    0 4px 5px 0 rgba(0, 0, 0, var(--md-shadow-key-penumbra-opacity)),
    0 1px 10px 0 rgba(0, 0, 0, var(--md-shadow-ambient-shadow-opacity)),
    0 2px 4px -1px rgba(0, 0, 0, var(--md-shadow-key-umbra-opacity));
  color: var(--jp-ui-font-color1);
  background-color: var(--jp-layout-color3);
}
.jupyter-button:focus:enabled {
  outline: 1px solid var(--jp-widgets-input-focus-border-color);
}
.jupyter-button.mod-primary {
  color: var(--jp-ui-inverse-font-color1);
  background-color: var(--jp-brand-color1);
}
.jupyter-button.mod-primary.mod-active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-brand-color0);
}
.jupyter-button.mod-primary:active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-brand-color0);
}
.jupyter-button.mod-success {
  color: var(--jp-ui-inverse-font-color1);
  background-color: var(--jp-success-color1);
}
.jupyter-button.mod-success.mod-active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-success-color0);
}
.jupyter-button.mod-success:active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-success-color0);
}
.jupyter-button.mod-info {
  color: var(--jp-ui-inverse-font-color1);
  background-color: var(--jp-info-color1);
}
.jupyter-button.mod-info.mod-active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-info-color0);
}
.jupyter-button.mod-info:active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-info-color0);
}
.jupyter-button.mod-warning {
  color: var(--jp-ui-inverse-font-color1);
  background-color: var(--jp-warn-color1);
}
.jupyter-button.mod-warning.mod-active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-warn-color0);
}
.jupyter-button.mod-warning:active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-warn-color0);
}
.jupyter-button.mod-danger {
  color: var(--jp-ui-inverse-font-color1);
  background-color: var(--jp-error-color1);
}
.jupyter-button.mod-danger.mod-active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-error-color0);
}
.jupyter-button.mod-danger:active {
  color: var(--jp-ui-inverse-font-color0);
  background-color: var(--jp-error-color0);
}
.widget-button,
.widget-toggle-button,
.widget-upload,
.jupyter-widget-button,
.jupyter-widget-toggle-button,
.jupyter-widget-upload {
  width: var(--jp-widgets-inline-width-short);
}
.jupyter-widgets label {
  margin-bottom: initial;
}
.widget-label-basic,
.jupyter-widget-label-basic {
  color: var(--jp-widgets-label-color);
  font-size: var(--jp-widgets-font-size);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: var(--jp-widgets-inline-height);
}
.widget-label,
.jupyter-widget-label {
  color: var(--jp-widgets-label-color);
  font-size: var(--jp-widgets-font-size);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: var(--jp-widgets-inline-height);
}
.widget-inline-hbox .widget-label,
.jupyter-widget-inline-hbox .jupyter-widget-label {
  color: var(--jp-widgets-label-color);
  text-align: right;
  margin-right: calc(var(--jp-widgets-inline-margin) * 2);
  width: var(--jp-widgets-inline-label-width);
  flex-shrink: 0;
}
.widget-inline-vbox .widget-label,
.jupyter-widget-inline-vbox .jupyter-widget-label {
  color: var(--jp-widgets-label-color);
  text-align: center;
  line-height: var(--jp-widgets-inline-height);
}
.widget-readout,
.jupyter-widget-readout {
  color: var(--jp-widgets-readout-color);
  font-size: var(--jp-widgets-font-size);
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
  overflow: hidden;
  white-space: nowrap;
  text-align: center;
}
.widget-readout.overflow,
.jupyter-widget-readout.overflow {
  -webkit-box-shadow:
    0 2px 2px 0 rgba(0, 0, 0, 0.2),
    0 3px 1px -2px rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow:
    0 2px 2px 0 rgba(0, 0, 0, 0.2),
    0 3px 1px -2px rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  box-shadow:
    0 2px 2px 0 rgba(0, 0, 0, 0.2),
    0 3px 1px -2px rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
.widget-inline-hbox .widget-readout,
.jupyter-widget-inline-hbox .jupyter-widget-readout {
  text-align: center;
  max-width: var(--jp-widgets-inline-width-short);
  min-width: var(--jp-widgets-inline-width-tiny);
  margin-left: var(--jp-widgets-inline-margin);
}
.widget-inline-vbox .widget-readout,
.jupyter-widget-inline-vbox .jupyter-widget-readout {
  margin-top: var(--jp-widgets-inline-margin);
  width: inherit;
}
.widget-checkbox,
.jupyter-widget-checkbox {
  width: var(--jp-widgets-inline-width);
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
}
.widget-checkbox input[type=checkbox],
.jupyter-widget-checkbox input[type=checkbox] {
  margin: 0px calc(var(--jp-widgets-inline-margin) * 2) 0px 0px;
  line-height: var(--jp-widgets-inline-height);
  font-size: large;
  flex-grow: 1;
  flex-shrink: 0;
  align-self: center;
}
.widget-valid,
.jupyter-widget-valid {
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
  width: var(--jp-widgets-inline-width-short);
  font-size: var(--jp-widgets-font-size);
}
.widget-valid i,
.jupyter-widget-valid i {
  line-height: var(--jp-widgets-inline-height);
  margin-right: var(--jp-widgets-inline-margin);
  margin-left: var(--jp-widgets-inline-margin);
}
.widget-valid.mod-valid i,
.jupyter-widget-valid.mod-valid i {
  color: green;
}
.widget-valid.mod-invalid i,
.jupyter-widget-valid.mod-invalid i {
  color: red;
}
.widget-valid.mod-valid .widget-valid-readout,
.jupyter-widget-valid.mod-valid .jupyter-widget-valid-readout {
  display: none;
}
.widget-textarea,
.widget-text,
.jupyter-widget-textarea,
.jupyter-widget-text {
  width: var(--jp-widgets-inline-width);
}
.widget-text input[type=text],
.widget-text input[type=number],
.widget-text input[type=password],
.jupyter-widget-text input[type=text],
.jupyter-widget-text input[type=number],
.jupyter-widget-text input[type=password] {
  height: var(--jp-widgets-inline-height);
}
.widget-text input[type=text]:disabled,
.widget-text input[type=number]:disabled,
.widget-text input[type=password]:disabled,
.widget-textarea textarea:disabled,
.jupyter-widget-text input[type=text]:disabled,
.jupyter-widget-text input[type=number]:disabled,
.jupyter-widget-text input[type=password]:disabled,
.jupyter-widget-textarea textarea:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.widget-text input[type=text],
.widget-text input[type=number],
.widget-text input[type=password],
.widget-textarea textarea,
.jupyter-widget-text input[type=text],
.jupyter-widget-text input[type=number],
.jupyter-widget-text input[type=password],
.jupyter-widget-textarea textarea {
  box-sizing: border-box;
  border: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
  background-color: var(--jp-widgets-input-background-color);
  color: var(--jp-widgets-input-color);
  font-size: var(--jp-widgets-font-size);
  flex-grow: 1;
  min-width: 0;
  flex-shrink: 1;
  outline: none !important;
}
.widget-text input[type=text],
.widget-text input[type=password],
.widget-textarea textarea,
.jupyter-widget-text input[type=text],
.jupyter-widget-text input[type=password],
.jupyter-widget-textarea textarea {
  padding: var(--jp-widgets-input-padding) calc(var(--jp-widgets-input-padding) * 2);
}
.widget-text input[type=number],
.jupyter-widget-text input[type=number] {
  padding: var(--jp-widgets-input-padding) 0 var(--jp-widgets-input-padding) calc(var(--jp-widgets-input-padding) * 2);
}
.widget-textarea textarea,
.jupyter-widget-textarea textarea {
  height: inherit;
  width: inherit;
}
.widget-text input:focus,
.widget-textarea textarea:focus,
.jupyter-widget-text input:focus,
.jupyter-widget-textarea textarea:focus {
  border-color: var(--jp-widgets-input-focus-border-color);
}
.widget-hslider,
.jupyter-widget-hslider {
  width: var(--jp-widgets-inline-width);
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
  align-items: center;
}
.widgets-slider .slider-container,
.jupyter-widgets-slider .slider-container {
  overflow: visible;
}
.widget-hslider .slider-container,
.jupyter-widget-hslider .slider-container {
  margin-left: calc(var(--jp-widgets-slider-handle-size) / 2 - 2 * var(--jp-widgets-slider-border-width));
  margin-right: calc(var(--jp-widgets-slider-handle-size) / 2 - 2 * var(--jp-widgets-slider-border-width));
  flex: 1 1 var(--jp-widgets-inline-width-short);
}
.widget-vbox .widget-label,
.jupyter-widget-vbox .jupyter-widget-label {
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
}
.widget-vslider,
.jupyter-widget-vslider {
  height: var(--jp-widgets-vertical-height);
  width: var(--jp-widgets-inline-width-tiny);
}
.widget-vslider .slider-container,
.jupyter-widget-vslider .slider-container {
  flex: 1 1 var(--jp-widgets-inline-width-short);
  margin-left: auto;
  margin-right: auto;
  margin-bottom: calc(var(--jp-widgets-slider-handle-size) / 2 - 2 * var(--jp-widgets-slider-border-width));
  margin-top: calc(var(--jp-widgets-slider-handle-size) / 2 - 2 * var(--jp-widgets-slider-border-width));
  display: flex;
  flex-direction: column;
}
.progress-bar {
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}
.progress-bar {
  height: var(--jp-widgets-inline-height);
}
.progress-bar {
  background-color: var(--jp-brand-color1);
}
.progress-bar-success {
  background-color: var(--jp-success-color1);
}
.progress-bar-info {
  background-color: var(--jp-info-color1);
}
.progress-bar-warning {
  background-color: var(--jp-warn-color1);
}
.progress-bar-danger {
  background-color: var(--jp-error-color1);
}
.progress {
  background-color: var(--jp-layout-color2);
  border: none;
  box-shadow: none;
}
.widget-hprogress,
.jupyter-widget-hprogress {
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
  width: var(--jp-widgets-inline-width);
  align-items: center;
}
.widget-hprogress .progress,
.jupyter-widget-hprogress .progress {
  flex-grow: 1;
  margin-top: var(--jp-widgets-input-padding);
  margin-bottom: var(--jp-widgets-input-padding);
  align-self: stretch;
  height: initial;
}
.widget-vprogress,
.jupyter-widget-vprogress {
  height: var(--jp-widgets-vertical-height);
  width: var(--jp-widgets-inline-width-tiny);
}
.widget-vprogress .progress,
.jupyter-widget-vprogress .progress {
  flex-grow: 1;
  width: var(--jp-widgets-progress-thickness);
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
}
.widget-dropdown,
.jupyter-widget-dropdown {
  height: var(--jp-widgets-inline-height);
  width: var(--jp-widgets-inline-width);
  line-height: var(--jp-widgets-inline-height);
}
.widget-dropdown > select,
.jupyter-widget-dropdown > select {
  padding-right: 20px;
  border: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
  border-radius: 0;
  height: inherit;
  flex: 1 1 var(--jp-widgets-inline-width-short);
  min-width: 0;
  box-sizing: border-box;
  outline: none !important;
  box-shadow: none;
  background-color: var(--jp-widgets-input-background-color);
  color: var(--jp-widgets-input-color);
  font-size: var(--jp-widgets-font-size);
  vertical-align: top;
  padding-left: calc(var(--jp-widgets-input-padding) * 2);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: right center;
  background-image: var(--jp-widgets-dropdown-arrow);
}
.widget-dropdown > select:focus,
.jupyter-widget-dropdown > select:focus {
  border-color: var(--jp-widgets-input-focus-border-color);
}
.widget-dropdown > select:disabled,
.jupyter-widget-dropdown > select:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.widget-dropdown > select:-moz-focusring,
.jupyter-widget-dropdown > select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}
.widget-select,
.jupyter-widget-select {
  width: var(--jp-widgets-inline-width);
  line-height: var(--jp-widgets-inline-height);
  align-items: flex-start;
}
.widget-select > select,
.jupyter-widget-select > select {
  border: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
  background-color: var(--jp-widgets-input-background-color);
  color: var(--jp-widgets-input-color);
  font-size: var(--jp-widgets-font-size);
  flex: 1 1 var(--jp-widgets-inline-width-short);
  outline: none !important;
  overflow: auto;
  height: inherit;
  padding-top: 5px;
}
.widget-select > select:focus,
.jupyter-widget-select > select:focus {
  border-color: var(--jp-widgets-input-focus-border-color);
}
.wiget-select > select > option,
.jupyter-wiget-select > select > option {
  padding-left: var(--jp-widgets-input-padding);
  line-height: var(--jp-widgets-inline-height);
  padding-top: calc(var(--jp-widgets-inline-height) - var(--jp-widgets-font-size) / 2);
  padding-bottom: calc(var(--jp-widgets-inline-height) - var(--jp-widgets-font-size) / 2);
}
.widget-toggle-buttons,
.jupyter-widget-toggle-buttons {
  line-height: var(--jp-widgets-inline-height);
}
.widget-toggle-buttons .widget-toggle-button,
.jupyter-widget-toggle-buttons .jupyter-widget-toggle-button {
  margin-left: var(--jp-widgets-margin);
  margin-right: var(--jp-widgets-margin);
}
.widget-toggle-buttons .jupyter-button:disabled,
.jupyter-widget-toggle-buttons .jupyter-button:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.widget-radio,
.jupyter-widget-radio {
  width: var(--jp-widgets-inline-width);
  line-height: var(--jp-widgets-inline-height);
}
.widget-radio-box,
.jupyter-widget-radio-box {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-sizing: border-box;
  flex-grow: 1;
  margin-bottom: var(--jp-widgets-radio-item-height-adjustment);
}
.widget-radio-box-vertical,
.jupyter-widget-radio-box-vertical {
  flex-direction: column;
}
.widget-radio-box-horizontal,
.jupyter-widget-radio-box-horizontal {
  flex-direction: row;
}
.widget-radio-box label,
.jupyter-widget-radio-box label {
  height: var(--jp-widgets-radio-item-height);
  line-height: var(--jp-widgets-radio-item-height);
  font-size: var(--jp-widgets-font-size);
}
.widget-radio-box-horizontal label,
.jupyter-widget-radio-box-horizontal label {
  margin: 0 calc(var(--jp-widgets-input-padding) * 2) 0 0;
}
.widget-radio-box input,
.jupyter-widget-radio-box input {
  height: var(--jp-widgets-radio-item-height);
  line-height: var(--jp-widgets-radio-item-height);
  margin: 0 calc(var(--jp-widgets-input-padding) * 2) 0 1px;
  float: left;
}
.widget-colorpicker,
.jupyter-widget-colorpicker {
  width: var(--jp-widgets-inline-width);
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
}
.widget-colorpicker > .widget-colorpicker-input,
.jupyter-widget-colorpicker > .jupyter-widget-colorpicker-input {
  flex-grow: 1;
  flex-shrink: 1;
  min-width: var(--jp-widgets-inline-width-tiny);
}
.widget-colorpicker input[type=color],
.jupyter-widget-colorpicker input[type=color] {
  width: var(--jp-widgets-inline-height);
  height: var(--jp-widgets-inline-height);
  padding: 0 2px;
  background: var(--jp-widgets-input-background-color);
  color: var(--jp-widgets-input-color);
  border: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
  border-left: none;
  flex-grow: 0;
  flex-shrink: 0;
  box-sizing: border-box;
  align-self: stretch;
  outline: none !important;
}
.widget-colorpicker.concise input[type=color],
.jupyter-widget-colorpicker.concise input[type=color] {
  border-left: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
}
.widget-colorpicker input[type=color]:focus,
.widget-colorpicker input[type=text]:focus,
.jupyter-widget-colorpicker input[type=color]:focus,
.jupyter-widget-colorpicker input[type=text]:focus {
  border-color: var(--jp-widgets-input-focus-border-color);
}
.widget-colorpicker input[type=text],
.jupyter-widget-colorpicker input[type=text] {
  flex-grow: 1;
  outline: none !important;
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
  background: var(--jp-widgets-input-background-color);
  color: var(--jp-widgets-input-color);
  border: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
  font-size: var(--jp-widgets-font-size);
  padding: var(--jp-widgets-input-padding) calc(var(--jp-widgets-input-padding) * 2);
  min-width: 0;
  flex-shrink: 1;
  box-sizing: border-box;
}
.widget-colorpicker input[type=text]:disabled,
.jupyter-widget-colorpicker input[type=text]:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.widget-datepicker,
.jupyter-widget-datepicker {
  width: var(--jp-widgets-inline-width);
  height: var(--jp-widgets-inline-height);
  line-height: var(--jp-widgets-inline-height);
}
.widget-datepicker input[type=date],
.jupyter-widget-datepicker input[type=date] {
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
  outline: none !important;
  height: var(--jp-widgets-inline-height);
  border: var(--jp-widgets-input-border-width) solid var(--jp-widgets-input-border-color);
  background-color: var(--jp-widgets-input-background-color);
  color: var(--jp-widgets-input-color);
  font-size: var(--jp-widgets-font-size);
  padding: var(--jp-widgets-input-padding) calc(var(--jp-widgets-input-padding) * 2);
  box-sizing: border-box;
}
.widget-datepicker input[type=date]:focus,
.jupyter-widget-datepicker input[type=date]:focus {
  border-color: var(--jp-widgets-input-focus-border-color);
}
.widget-datepicker input[type=date]:invalid,
.jupyter-widget-datepicker input[type=date]:invalid {
  border-color: var(--jp-warn-color1);
}
.widget-datepicker input[type=date]:disabled,
.jupyter-widget-datepicker input[type=date]:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.widget-play,
.jupyter-widget-play {
  width: var(--jp-widgets-inline-width-short);
  display: flex;
  align-items: stretch;
}
.widget-play .jupyter-button,
.jupyter-widget-play .jupyter-button {
  flex-grow: 1;
  height: auto;
}
.widget-play .jupyter-button:disabled,
.jupyter-widget-play .jupyter-button:disabled {
  opacity: var(--jp-widgets-disabled-opacity);
}
.jupyter-widgets.widget-tab,
.jupyter-widgets.jupyter-widget-tab {
  display: flex;
  flex-direction: column;
}
.jupyter-widgets.widget-tab > .p-TabBar,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar {
  overflow-x: visible;
  overflow-y: visible;
}
.jupyter-widgets.widget-tab > .p-TabBar > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar > .p-TabBar-content,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar > .lm-TabBar-content {
  align-items: flex-end;
  min-width: 0;
  min-height: 0;
}
.jupyter-widgets.widget-tab > .widget-tab-contents,
.jupyter-widgets.jupyter-widget-tab > .widget-tab-contents {
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  background: var(--jp-layout-color1);
  color: var(--jp-ui-font-color1);
  border: var(--jp-border-width) solid var(--jp-border-color1);
  padding: var(--jp-widgets-container-padding);
  flex-grow: 1;
  overflow: auto;
}
.jupyter-widgets.widget-tab > .p-TabBar,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar {
  font:
    var(--jp-widgets-font-size) Helvetica,
    Arial,
    sans-serif;
  min-height: calc(var(--jp-widgets-horizontal-tab-height) + var(--jp-border-width));
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab {
  flex: 0 1 var(--jp-widgets-horizontal-tab-width);
  min-width: 35px;
  min-height: calc(var(--jp-widgets-horizontal-tab-height) + var(--jp-border-width));
  line-height: var(--jp-widgets-horizontal-tab-height);
  margin-left: calc(-1 * var(--jp-border-width));
  padding: 0px 10px;
  background: var(--jp-layout-color2);
  color: var(--jp-ui-font-color2);
  border: var(--jp-border-width) solid var(--jp-border-color1);
  border-bottom: none;
  position: relative;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab.p-mod-current,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab.p-mod-current,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab.lm-mod-current {
  color: var(--jp-ui-font-color0);
  background: var(--jp-layout-color1);
  min-height: calc(var(--jp-widgets-horizontal-tab-height) + 2 * var(--jp-border-width));
  transform: translateY(var(--jp-border-width));
  overflow: visible;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab.p-mod-current:before,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab.p-mod-current:before,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab.lm-mod-current:before {
  position: absolute;
  top: calc(-1 * var(--jp-border-width));
  left: calc(-1 * var(--jp-border-width));
  content: "";
  height: var(--jp-widgets-horizontal-tab-top-border);
  width: calc(100% + 2 * var(--jp-border-width));
  background: var(--jp-brand-color1);
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab:first-child,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab:first-child,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab:first-child {
  margin-left: 0;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tab:hover:not(.p-mod-current),
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tab:hover:not(.p-mod-current),
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tab:hover:not(.lm-mod-current) {
  background: var(--jp-layout-color1);
  color: var(--jp-ui-font-color1);
}
.jupyter-widgets.widget-tab > .p-TabBar .p-mod-closable > .p-TabBar-tabCloseIcon,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-mod-closable > .p-TabBar-tabCloseIcon,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-mod-closable > .lm-TabBar-tabCloseIcon {
  margin-left: 4px;
}
.jupyter-widgets.widget-tab > .p-TabBar .p-mod-closable > .p-TabBar-tabCloseIcon:before,
.jupyter-widgets.jupyter-widget-widget-tab > .p-TabBar .p-mod-closable > .p-TabBar-tabCloseIcon:before,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-mod-closable > .lm-TabBar-tabCloseIcon:before {
  font-family: FontAwesome;
  content: "\f00d";
}
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tabIcon,
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tabLabel,
.jupyter-widgets.widget-tab > .p-TabBar .p-TabBar-tabCloseIcon,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tabIcon,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tabLabel,
.jupyter-widgets.jupyter-widget-tab > .p-TabBar .p-TabBar-tabCloseIcon,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tabIcon,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tabLabel,
.jupyter-widgets.jupyter-widget-tab > .lm-TabBar .lm-TabBar-tabCloseIcon {
  line-height: var(--jp-widgets-horizontal-tab-height);
}
.jupyter-widget-Collapse {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.jupyter-widget-Collapse-header {
  padding: var(--jp-widgets-input-padding);
  cursor: pointer;
  color: var(--jp-ui-font-color2);
  background-color: var(--jp-layout-color2);
  border: var(--jp-widgets-border-width) solid var(--jp-border-color1);
  padding: calc(var(--jp-widgets-container-padding) * 2 / 3) var(--jp-widgets-container-padding);
  font-weight: bold;
}
.jupyter-widget-Collapse-header:hover {
  background-color: var(--jp-layout-color1);
  color: var(--jp-ui-font-color1);
}
.jupyter-widget-Collapse-open > .jupyter-widget-Collapse-header {
  background-color: var(--jp-layout-color1);
  color: var(--jp-ui-font-color0);
  cursor: default;
  border-bottom: none;
}
.jupyter-widget-Collapse-contents {
  padding: var(--jp-widgets-container-padding);
  background-color: var(--jp-layout-color1);
  color: var(--jp-ui-font-color1);
  border-left: var(--jp-widgets-border-width) solid var(--jp-border-color1);
  border-right: var(--jp-widgets-border-width) solid var(--jp-border-color1);
  border-bottom: var(--jp-widgets-border-width) solid var(--jp-border-color1);
  overflow: auto;
}
.jupyter-widget-Accordion {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.jupyter-widget-Accordion .jupyter-widget-Collapse {
  margin-bottom: 0;
}
.jupyter-widget-Accordion .jupyter-widget-Collapse + .jupyter-widget-Collapse {
  margin-top: 4px;
}
.widget-html,
.widget-htmlmath,
.jupyter-widget-html,
.jupyter-widget-htmlmath {
  font-size: var(--jp-widgets-font-size);
}
.widget-html > .widget-html-content,
.widget-htmlmath > .widget-html-content,
.jupyter-widget-html > .jupyter-widget-html-content,
.jupyter-widget-htmlmath > .jupyter-widget-html-content {
  align-self: stretch;
  flex-grow: 1;
  flex-shrink: 1;
  line-height: var(--jp-widgets-inline-height);
  position: relative;
}
.widget-image,
.jupyter-widget-image {
  max-width: 100%;
  height: auto;
}

/* ../../node_modules/@lumino/widgets/style/widget.css */
.lm-Widget {
  box-sizing: border-box;
  position: relative;
}
.lm-Widget.lm-mod-hidden {
  display: none !important;
}

/* ../../node_modules/@lumino/widgets/style/accordionpanel.css */
.lm-AccordionPanel[data-orientation=horizontal] > .lm-AccordionPanel-title {
  display: block;
  transform-origin: top left;
  transform: rotate(-90deg) translate(-100%);
}

/* ../../node_modules/@lumino/widgets/style/commandpalette.css */
.lm-CommandPalette {
  display: flex;
  flex-direction: column;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.lm-CommandPalette-search {
  flex: 0 0 auto;
}
.lm-CommandPalette-content {
  flex: 1 1 auto;
  margin: 0;
  padding: 0;
  min-height: 0;
  overflow: auto;
  list-style-type: none;
}
.lm-CommandPalette-header {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.lm-CommandPalette-item {
  display: flex;
  flex-direction: row;
}
.lm-CommandPalette-itemIcon {
  flex: 0 0 auto;
}
.lm-CommandPalette-itemContent {
  flex: 1 1 auto;
  overflow: hidden;
}
.lm-CommandPalette-itemShortcut {
  flex: 0 0 auto;
}
.lm-CommandPalette-itemLabel {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.lm-close-icon {
  border: 1px solid transparent;
  background-color: transparent;
  position: absolute;
  z-index: 1;
  right: 3%;
  top: 0;
  bottom: 0;
  margin: auto;
  padding: 7px 0;
  display: none;
  vertical-align: middle;
  outline: 0;
  cursor: pointer;
}
.lm-close-icon:after {
  content: "X";
  display: block;
  width: 15px;
  height: 15px;
  text-align: center;
  color: #000;
  font-weight: normal;
  font-size: 12px;
  cursor: pointer;
}

/* ../../node_modules/@lumino/widgets/style/dockpanel.css */
.lm-DockPanel {
  z-index: 0;
}
.lm-DockPanel-widget {
  z-index: 0;
}
.lm-DockPanel-tabBar {
  z-index: 1;
}
.lm-DockPanel-handle {
  z-index: 2;
}
.lm-DockPanel-handle.lm-mod-hidden {
  display: none !important;
}
.lm-DockPanel-handle:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
}
.lm-DockPanel-handle[data-orientation=horizontal] {
  cursor: ew-resize;
}
.lm-DockPanel-handle[data-orientation=vertical] {
  cursor: ns-resize;
}
.lm-DockPanel-handle[data-orientation=horizontal]:after {
  left: 50%;
  min-width: 8px;
  transform: translateX(-50%);
}
.lm-DockPanel-handle[data-orientation=vertical]:after {
  top: 50%;
  min-height: 8px;
  transform: translateY(-50%);
}
.lm-DockPanel-overlay {
  z-index: 3;
  box-sizing: border-box;
  pointer-events: none;
}
.lm-DockPanel-overlay.lm-mod-hidden {
  display: none !important;
}

/* ../../node_modules/@lumino/widgets/style/menu.css */
.lm-Menu {
  z-index: 10000;
  position: absolute;
  white-space: nowrap;
  overflow-x: hidden;
  overflow-y: auto;
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.lm-Menu-content {
  margin: 0;
  padding: 0;
  display: table;
  list-style-type: none;
}
.lm-Menu-item {
  display: table-row;
}
.lm-Menu-item.lm-mod-hidden,
.lm-Menu-item.lm-mod-collapsed {
  display: none !important;
}
.lm-Menu-itemIcon,
.lm-Menu-itemSubmenuIcon {
  display: table-cell;
  text-align: center;
}
.lm-Menu-itemLabel {
  display: table-cell;
  text-align: left;
}
.lm-Menu-itemShortcut {
  display: table-cell;
  text-align: right;
}

/* ../../node_modules/@lumino/widgets/style/menubar.css */
.lm-MenuBar {
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.lm-MenuBar-content {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  list-style-type: none;
}
.lm-MenuBar-item {
  box-sizing: border-box;
}
.lm-MenuBar-itemIcon,
.lm-MenuBar-itemLabel {
  display: inline-block;
}

/* ../../node_modules/@lumino/widgets/style/scrollbar.css */
.lm-ScrollBar {
  display: flex;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.lm-ScrollBar[data-orientation=horizontal] {
  flex-direction: row;
}
.lm-ScrollBar[data-orientation=vertical] {
  flex-direction: column;
}
.lm-ScrollBar-button {
  box-sizing: border-box;
  flex: 0 0 auto;
}
.lm-ScrollBar-track {
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  flex: 1 1 auto;
}
.lm-ScrollBar-thumb {
  box-sizing: border-box;
  position: absolute;
}

/* ../../node_modules/@lumino/widgets/style/splitpanel.css */
.lm-SplitPanel-child {
  z-index: 0;
}
.lm-SplitPanel-handle {
  z-index: 1;
}
.lm-SplitPanel-handle.lm-mod-hidden {
  display: none !important;
}
.lm-SplitPanel-handle:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
}
.lm-SplitPanel[data-orientation=horizontal] > .lm-SplitPanel-handle {
  cursor: ew-resize;
}
.lm-SplitPanel[data-orientation=vertical] > .lm-SplitPanel-handle {
  cursor: ns-resize;
}
.lm-SplitPanel[data-orientation=horizontal] > .lm-SplitPanel-handle:after {
  left: 50%;
  min-width: 8px;
  transform: translateX(-50%);
}
.lm-SplitPanel[data-orientation=vertical] > .lm-SplitPanel-handle:after {
  top: 50%;
  min-height: 8px;
  transform: translateY(-50%);
}

/* ../../node_modules/@lumino/widgets/style/tabbar.css */
.lm-TabBar {
  display: flex;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.lm-TabBar[data-orientation=horizontal] {
  flex-direction: row;
  align-items: flex-end;
}
.lm-TabBar[data-orientation=vertical] {
  flex-direction: column;
  align-items: flex-end;
}
.lm-TabBar-content {
  margin: 0;
  padding: 0;
  display: flex;
  flex: 1 1 auto;
  list-style-type: none;
}
.lm-TabBar[data-orientation=horizontal] > .lm-TabBar-content {
  flex-direction: row;
}
.lm-TabBar[data-orientation=vertical] > .lm-TabBar-content {
  flex-direction: column;
}
.lm-TabBar-tab {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  overflow: hidden;
  touch-action: none;
}
.lm-TabBar-tabIcon,
.lm-TabBar-tabCloseIcon {
  flex: 0 0 auto;
}
.lm-TabBar-tabLabel {
  flex: 1 1 auto;
  overflow: hidden;
  white-space: nowrap;
}
.lm-TabBar-tabInput {
  user-select: all;
  width: 100%;
  box-sizing: border-box;
}
.lm-TabBar-tab.lm-mod-hidden {
  display: none !important;
}
.lm-TabBar-addButton.lm-mod-hidden {
  display: none !important;
}
.lm-TabBar.lm-mod-dragging .lm-TabBar-tab {
  position: relative;
}
.lm-TabBar.lm-mod-dragging[data-orientation=horizontal] .lm-TabBar-tab {
  left: 0;
  transition: left 150ms ease;
}
.lm-TabBar.lm-mod-dragging[data-orientation=vertical] .lm-TabBar-tab {
  top: 0;
  transition: top 150ms ease;
}
.lm-TabBar.lm-mod-dragging .lm-TabBar-tab.lm-mod-dragging {
  transition: none;
}
.lm-TabBar-tabLabel .lm-TabBar-tabInput {
  user-select: all;
  width: 100%;
  box-sizing: border-box;
  background: inherit;
}

/* ../../node_modules/@lumino/widgets/style/tabpanel.css */
.lm-TabPanel-tabBar {
  z-index: 1;
}
.lm-TabPanel-stackedPanel {
  z-index: 0;
}

/* ../../node_modules/@lumino/widgets/style/index.css */

/* ../../node_modules/@jupyterlab/apputils/style/commandpalette.css */
:root {
  --jp-private-commandpalette-search-height: 28px;
}
.lm-CommandPalette {
  padding-bottom: 0;
  color: var(--jp-ui-font-color1);
  background: var(--jp-layout-color1);
  font-size: var(--jp-ui-font-size1);
}
.jp-ModalCommandPalette {
  position: absolute;
  z-index: 10000;
  top: 38px;
  left: 30%;
  margin: 0;
  padding: 4px;
  width: 40%;
  box-shadow: var(--jp-elevation-z4);
  border-radius: 4px;
  background: var(--jp-layout-color0);
}
.jp-ModalCommandPalette .lm-CommandPalette {
  max-height: 40vh;
}
.jp-ModalCommandPalette .lm-CommandPalette .lm-close-icon::after {
  display: none;
}
.jp-ModalCommandPalette .lm-CommandPalette .lm-CommandPalette-header {
  display: none;
}
.jp-ModalCommandPalette .lm-CommandPalette .lm-CommandPalette-item {
  margin-left: 4px;
  margin-right: 4px;
}
.jp-ModalCommandPalette .lm-CommandPalette .lm-CommandPalette-item.lm-mod-disabled {
  display: none;
}
.lm-CommandPalette-search {
  padding: 4px;
  background-color: var(--jp-layout-color1);
  z-index: 2;
}
.lm-CommandPalette-wrapper {
  overflow: overlay;
  padding: 0 9px;
  background-color: var(--jp-input-active-background);
  height: 30px;
  box-shadow: inset 0 0 0 var(--jp-border-width) var(--jp-input-border-color);
}
.lm-CommandPalette.lm-mod-focused .lm-CommandPalette-wrapper {
  box-shadow: inset 0 0 0 1px var(--jp-input-active-box-shadow-color), inset 0 0 0 3px var(--jp-input-active-box-shadow-color);
}
.jp-SearchIconGroup {
  color: white;
  background-color: var(--jp-brand-color1);
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 5px 5px 1px;
}
.jp-SearchIconGroup svg {
  height: 20px;
  width: 20px;
}
.jp-SearchIconGroup .jp-icon3[fill] {
  fill: var(--jp-layout-color0);
}
.lm-CommandPalette-input {
  background: transparent;
  width: calc(100% - 18px);
  float: left;
  border: none;
  outline: none;
  font-size: var(--jp-ui-font-size1);
  color: var(--jp-ui-font-color0);
  line-height: var(--jp-private-commandpalette-search-height);
}
.lm-CommandPalette-input::-webkit-input-placeholder,
.lm-CommandPalette-input::-moz-placeholder,
.lm-CommandPalette-input:-ms-input-placeholder {
  color: var(--jp-ui-font-color2);
  font-size: var(--jp-ui-font-size1);
}
.lm-CommandPalette-header:first-child {
  margin-top: 0;
}
.lm-CommandPalette-header {
  border-bottom: solid var(--jp-border-width) var(--jp-border-color2);
  color: var(--jp-ui-font-color1);
  cursor: pointer;
  display: flex;
  font-size: var(--jp-ui-font-size0);
  font-weight: 600;
  letter-spacing: 1px;
  margin-top: 8px;
  padding: 8px 0 8px 12px;
  text-transform: uppercase;
}
.lm-CommandPalette-header.lm-mod-active {
  background: var(--jp-layout-color2);
}
.lm-CommandPalette-header > mark {
  background-color: transparent;
  font-weight: bold;
  color: var(--jp-ui-font-color1);
}
.lm-CommandPalette-item {
  padding: 4px 12px 4px 4px;
  color: var(--jp-ui-font-color1);
  font-size: var(--jp-ui-font-size1);
  font-weight: 400;
  display: flex;
}
.lm-CommandPalette-item.lm-mod-disabled {
  color: var(--jp-ui-font-color2);
}
.lm-CommandPalette-item.lm-mod-active {
  color: var(--jp-ui-inverse-font-color1);
  background: var(--jp-brand-color1);
}
.lm-CommandPalette-item.lm-mod-active .lm-CommandPalette-itemLabel > mark {
  color: var(--jp-ui-inverse-font-color0);
}
.lm-CommandPalette-item.lm-mod-active .jp-icon-selectable[fill] {
  fill: var(--jp-layout-color0);
}
.lm-CommandPalette-item.lm-mod-active:hover:not(.lm-mod-disabled) {
  color: var(--jp-ui-inverse-font-color1);
  background: var(--jp-brand-color1);
}
.lm-CommandPalette-item:hover:not(.lm-mod-active):not(.lm-mod-disabled) {
  background: var(--jp-layout-color2);
}
.lm-CommandPalette-itemContent {
  overflow: hidden;
}
.lm-CommandPalette-itemLabel > mark {
  color: var(--jp-ui-font-color0);
  background-color: transparent;
  font-weight: bold;
}
.lm-CommandPalette-item.lm-mod-disabled mark {
  color: var(--jp-ui-font-color2);
}
.lm-CommandPalette-item .lm-CommandPalette-itemIcon {
  margin: 0 4px 0 0;
  position: relative;
  width: 16px;
  top: 2px;
  flex: 0 0 auto;
}
.lm-CommandPalette-item.lm-mod-disabled .lm-CommandPalette-itemIcon {
  opacity: 0.6;
}
.lm-CommandPalette-item .lm-CommandPalette-itemShortcut {
  flex: 0 0 auto;
}
.lm-CommandPalette-itemCaption {
  display: none;
}
.lm-CommandPalette-content {
  background-color: var(--jp-layout-color1);
}
.lm-CommandPalette-content:empty::after {
  content: "No results";
  margin: auto;
  margin-top: 20px;
  width: 100px;
  display: block;
  font-size: var(--jp-ui-font-size2);
  font-family: var(--jp-ui-font-family);
  font-weight: lighter;
}
.lm-CommandPalette-emptyMessage {
  text-align: center;
  margin-top: 24px;
  line-height: 1.32;
  padding: 0 8px;
  color: var(--jp-content-font-color3);
}

/* ../../node_modules/@jupyterlab/apputils/style/dialog.css */
.jp-Dialog {
  position: absolute;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  background: var(--jp-dialog-background);
}
.jp-Dialog-content {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  background: var(--jp-layout-color1);
  padding: 24px 24px 12px;
  min-width: 300px;
  min-height: 150px;
  max-width: 1000px;
  max-height: 500px;
  box-sizing: border-box;
  box-shadow: var(--jp-elevation-z20);
  word-wrap: break-word;
  border-radius: var(--jp-border-radius);
  font-size: var(--jp-ui-font-size1);
  color: var(--jp-ui-font-color1);
  resize: both;
}
.jp-Dialog-content.jp-Dialog-content-small {
  max-width: 500px;
}
.jp-Dialog-button {
  overflow: visible;
}
button.jp-Dialog-button:disabled {
  opacity: 0.6;
}
button.jp-Dialog-button:focus {
  outline: 1px solid var(--jp-brand-color1);
  outline-offset: 4px;
  -moz-outline-radius: 0;
}
button.jp-Dialog-button:focus::-moz-focus-inner {
  border: 0;
}
button.jp-Dialog-button.jp-mod-styled.jp-mod-accept:focus,
button.jp-Dialog-button.jp-mod-styled.jp-mod-warn:focus,
button.jp-Dialog-button.jp-mod-styled.jp-mod-reject:focus {
  outline-offset: 4px;
  -moz-outline-radius: 0;
}
button.jp-Dialog-button.jp-mod-styled.jp-mod-accept:focus {
  outline: 1px solid var(--jp-accept-color-normal, var(--jp-brand-color1));
}
button.jp-Dialog-button.jp-mod-styled.jp-mod-warn:focus {
  outline: 1px solid var(--jp-warn-color-normal, var(--jp-error-color1));
}
button.jp-Dialog-button.jp-mod-styled.jp-mod-reject:focus {
  outline: 1px solid var(--jp-reject-color-normal, var(--md-grey-600, #757575));
}
button.jp-Dialog-close-button {
  padding: 0;
  height: 100%;
  min-width: unset;
  min-height: unset;
}
.jp-Dialog-header {
  display: flex;
  justify-content: space-between;
  flex: 0 0 auto;
  padding-bottom: 12px;
  font-size: var(--jp-ui-font-size3);
  font-weight: 400;
  color: var(--jp-ui-font-color1);
}
.jp-Dialog-body {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  font-size: var(--jp-ui-font-size1);
  background: var(--jp-layout-color1);
  color: var(--jp-ui-font-color1);
  overflow: auto;
}
.jp-Dialog-footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex: 0 0 auto;
  margin-left: -12px;
  margin-right: -12px;
  padding: 12px;
}
.jp-Dialog-checkbox {
  padding-right: 5px;
}
.jp-Dialog-spacer {
  flex: 1 1 auto;
}
.jp-Dialog-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jp-Dialog-body > .jp-select-wrapper {
  width: 100%;
}
.jp-Dialog-body > button {
  padding: 0 16px;
}
.jp-Dialog-body > label {
  line-height: 1.4;
  color: var(--jp-ui-font-color0);
}
.jp-Dialog-button.jp-mod-styled:not(:last-child) {
  margin-right: 12px;
}

/* ../../node_modules/@jupyterlab/apputils/style/inputdialog.css */
.jp-Input-Boolean-Dialog {
  flex-direction: row-reverse;
  align-items: end;
  width: 100%;
}
.jp-Input-Boolean-Dialog > label {
  flex: 1 1 auto;
}
.jp-InputDialog-inputWrapper {
  display: flex;
  align-items: baseline;
}
.jp-InputDialog-inputWrapper > input.jp-mod-styled:invalid {
  border-color: var(--jp-error-color0);
  background: var(--jp-error-color3);
}
.jp-InputDialog-inputWrapper > input[required].jp-mod-styled:invalid:placeholder-shown {
  border-color: unset;
  background: unset;
}

/* ../../node_modules/@jupyterlab/apputils/style/mainareawidget.css */
.jp-MainAreaWidget > :focus {
  outline: none;
}
.jp-MainAreaWidget .jp-MainAreaWidget-error {
  padding: 6px;
}
.jp-MainAreaWidget .jp-MainAreaWidget-error > pre {
  width: auto;
  padding: 10px;
  background: var(--jp-error-color3);
  border: var(--jp-border-width) solid var(--jp-error-color1);
  border-radius: var(--jp-border-radius);
  color: var(--jp-ui-font-color1);
  font-size: var(--jp-ui-font-size1);
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* ../../node_modules/@jupyterlab/apputils/style/materialcolors.css */
:root {
  --md-red-50: #ffebee;
  --md-red-100: #ffcdd2;
  --md-red-200: #ef9a9a;
  --md-red-300: #e57373;
  --md-red-400: #ef5350;
  --md-red-500: #f44336;
  --md-red-600: #e53935;
  --md-red-700: #d32f2f;
  --md-red-800: #c62828;
  --md-red-900: #b71c1c;
  --md-red-A100: #ff8a80;
  --md-red-A200: #ff5252;
  --md-red-A400: #ff1744;
  --md-red-A700: #d50000;
  --md-pink-50: #fce4ec;
  --md-pink-100: #f8bbd0;
  --md-pink-200: #f48fb1;
  --md-pink-300: #f06292;
  --md-pink-400: #ec407a;
  --md-pink-500: #e91e63;
  --md-pink-600: #d81b60;
  --md-pink-700: #c2185b;
  --md-pink-800: #ad1457;
  --md-pink-900: #880e4f;
  --md-pink-A100: #ff80ab;
  --md-pink-A200: #ff4081;
  --md-pink-A400: #f50057;
  --md-pink-A700: #c51162;
  --md-purple-50: #f3e5f5;
  --md-purple-100: #e1bee7;
  --md-purple-200: #ce93d8;
  --md-purple-300: #ba68c8;
  --md-purple-400: #ab47bc;
  --md-purple-500: #9c27b0;
  --md-purple-600: #8e24aa;
  --md-purple-700: #7b1fa2;
  --md-purple-800: #6a1b9a;
  --md-purple-900: #4a148c;
  --md-purple-A100: #ea80fc;
  --md-purple-A200: #e040fb;
  --md-purple-A400: #d500f9;
  --md-purple-A700: #a0f;
  --md-deep-purple-50: #ede7f6;
  --md-deep-purple-100: #d1c4e9;
  --md-deep-purple-200: #b39ddb;
  --md-deep-purple-300: #9575cd;
  --md-deep-purple-400: #7e57c2;
  --md-deep-purple-500: #673ab7;
  --md-deep-purple-600: #5e35b1;
  --md-deep-purple-700: #512da8;
  --md-deep-purple-800: #4527a0;
  --md-deep-purple-900: #311b92;
  --md-deep-purple-A100: #b388ff;
  --md-deep-purple-A200: #7c4dff;
  --md-deep-purple-A400: #651fff;
  --md-deep-purple-A700: #6200ea;
  --md-indigo-50: #e8eaf6;
  --md-indigo-100: #c5cae9;
  --md-indigo-200: #9fa8da;
  --md-indigo-300: #7986cb;
  --md-indigo-400: #5c6bc0;
  --md-indigo-500: #3f51b5;
  --md-indigo-600: #3949ab;
  --md-indigo-700: #303f9f;
  --md-indigo-800: #283593;
  --md-indigo-900: #1a237e;
  --md-indigo-A100: #8c9eff;
  --md-indigo-A200: #536dfe;
  --md-indigo-A400: #3d5afe;
  --md-indigo-A700: #304ffe;
  --md-blue-50: #e3f2fd;
  --md-blue-100: #bbdefb;
  --md-blue-200: #90caf9;
  --md-blue-300: #64b5f6;
  --md-blue-400: #42a5f5;
  --md-blue-500: #2196f3;
  --md-blue-600: #1e88e5;
  --md-blue-700: #1976d2;
  --md-blue-800: #1565c0;
  --md-blue-900: #0d47a1;
  --md-blue-A100: #82b1ff;
  --md-blue-A200: #448aff;
  --md-blue-A400: #2979ff;
  --md-blue-A700: #2962ff;
  --md-light-blue-50: #e1f5fe;
  --md-light-blue-100: #b3e5fc;
  --md-light-blue-200: #81d4fa;
  --md-light-blue-300: #4fc3f7;
  --md-light-blue-400: #29b6f6;
  --md-light-blue-500: #03a9f4;
  --md-light-blue-600: #039be5;
  --md-light-blue-700: #0288d1;
  --md-light-blue-800: #0277bd;
  --md-light-blue-900: #01579b;
  --md-light-blue-A100: #80d8ff;
  --md-light-blue-A200: #40c4ff;
  --md-light-blue-A400: #00b0ff;
  --md-light-blue-A700: #0091ea;
  --md-cyan-50: #e0f7fa;
  --md-cyan-100: #b2ebf2;
  --md-cyan-200: #80deea;
  --md-cyan-300: #4dd0e1;
  --md-cyan-400: #26c6da;
  --md-cyan-500: #00bcd4;
  --md-cyan-600: #00acc1;
  --md-cyan-700: #0097a7;
  --md-cyan-800: #00838f;
  --md-cyan-900: #006064;
  --md-cyan-A100: #84ffff;
  --md-cyan-A200: #18ffff;
  --md-cyan-A400: #00e5ff;
  --md-cyan-A700: #00b8d4;
  --md-teal-50: #e0f2f1;
  --md-teal-100: #b2dfdb;
  --md-teal-200: #80cbc4;
  --md-teal-300: #4db6ac;
  --md-teal-400: #26a69a;
  --md-teal-500: #009688;
  --md-teal-600: #00897b;
  --md-teal-700: #00796b;
  --md-teal-800: #00695c;
  --md-teal-900: #004d40;
  --md-teal-A100: #a7ffeb;
  --md-teal-A200: #64ffda;
  --md-teal-A400: #1de9b6;
  --md-teal-A700: #00bfa5;
  --md-green-50: #e8f5e9;
  --md-green-100: #c8e6c9;
  --md-green-200: #a5d6a7;
  --md-green-300: #81c784;
  --md-green-400: #66bb6a;
  --md-green-500: #4caf50;
  --md-green-600: #43a047;
  --md-green-700: #388e3c;
  --md-green-800: #2e7d32;
  --md-green-900: #1b5e20;
  --md-green-A100: #b9f6ca;
  --md-green-A200: #69f0ae;
  --md-green-A400: #00e676;
  --md-green-A700: #00c853;
  --md-light-green-50: #f1f8e9;
  --md-light-green-100: #dcedc8;
  --md-light-green-200: #c5e1a5;
  --md-light-green-300: #aed581;
  --md-light-green-400: #9ccc65;
  --md-light-green-500: #8bc34a;
  --md-light-green-600: #7cb342;
  --md-light-green-700: #689f38;
  --md-light-green-800: #558b2f;
  --md-light-green-900: #33691e;
  --md-light-green-A100: #ccff90;
  --md-light-green-A200: #b2ff59;
  --md-light-green-A400: #76ff03;
  --md-light-green-A700: #64dd17;
  --md-lime-50: #f9fbe7;
  --md-lime-100: #f0f4c3;
  --md-lime-200: #e6ee9c;
  --md-lime-300: #dce775;
  --md-lime-400: #d4e157;
  --md-lime-500: #cddc39;
  --md-lime-600: #c0ca33;
  --md-lime-700: #afb42b;
  --md-lime-800: #9e9d24;
  --md-lime-900: #827717;
  --md-lime-A100: #f4ff81;
  --md-lime-A200: #eeff41;
  --md-lime-A400: #c6ff00;
  --md-lime-A700: #aeea00;
  --md-yellow-50: #fffde7;
  --md-yellow-100: #fff9c4;
  --md-yellow-200: #fff59d;
  --md-yellow-300: #fff176;
  --md-yellow-400: #ffee58;
  --md-yellow-500: #ffeb3b;
  --md-yellow-600: #fdd835;
  --md-yellow-700: #fbc02d;
  --md-yellow-800: #f9a825;
  --md-yellow-900: #f57f17;
  --md-yellow-A100: #ffff8d;
  --md-yellow-A200: #ff0;
  --md-yellow-A400: #ffea00;
  --md-yellow-A700: #ffd600;
  --md-amber-50: #fff8e1;
  --md-amber-100: #ffecb3;
  --md-amber-200: #ffe082;
  --md-amber-300: #ffd54f;
  --md-amber-400: #ffca28;
  --md-amber-500: #ffc107;
  --md-amber-600: #ffb300;
  --md-amber-700: #ffa000;
  --md-amber-800: #ff8f00;
  --md-amber-900: #ff6f00;
  --md-amber-A100: #ffe57f;
  --md-amber-A200: #ffd740;
  --md-amber-A400: #ffc400;
  --md-amber-A700: #ffab00;
  --md-orange-50: #fff3e0;
  --md-orange-100: #ffe0b2;
  --md-orange-200: #ffcc80;
  --md-orange-300: #ffb74d;
  --md-orange-400: #ffa726;
  --md-orange-500: #ff9800;
  --md-orange-600: #fb8c00;
  --md-orange-700: #f57c00;
  --md-orange-800: #ef6c00;
  --md-orange-900: #e65100;
  --md-orange-A100: #ffd180;
  --md-orange-A200: #ffab40;
  --md-orange-A400: #ff9100;
  --md-orange-A700: #ff6d00;
  --md-deep-orange-50: #fbe9e7;
  --md-deep-orange-100: #ffccbc;
  --md-deep-orange-200: #ffab91;
  --md-deep-orange-300: #ff8a65;
  --md-deep-orange-400: #ff7043;
  --md-deep-orange-500: #ff5722;
  --md-deep-orange-600: #f4511e;
  --md-deep-orange-700: #e64a19;
  --md-deep-orange-800: #d84315;
  --md-deep-orange-900: #bf360c;
  --md-deep-orange-A100: #ff9e80;
  --md-deep-orange-A200: #ff6e40;
  --md-deep-orange-A400: #ff3d00;
  --md-deep-orange-A700: #dd2c00;
  --md-brown-50: #efebe9;
  --md-brown-100: #d7ccc8;
  --md-brown-200: #bcaaa4;
  --md-brown-300: #a1887f;
  --md-brown-400: #8d6e63;
  --md-brown-500: #795548;
  --md-brown-600: #6d4c41;
  --md-brown-700: #5d4037;
  --md-brown-800: #4e342e;
  --md-brown-900: #3e2723;
  --md-grey-50: #fafafa;
  --md-grey-100: #f5f5f5;
  --md-grey-200: #eee;
  --md-grey-300: #e0e0e0;
  --md-grey-400: #bdbdbd;
  --md-grey-500: #9e9e9e;
  --md-grey-600: #757575;
  --md-grey-700: #616161;
  --md-grey-800: #424242;
  --md-grey-900: #212121;
  --md-blue-grey-50: #eceff1;
  --md-blue-grey-100: #cfd8dc;
  --md-blue-grey-200: #b0bec5;
  --md-blue-grey-300: #90a4ae;
  --md-blue-grey-400: #78909c;
  --md-blue-grey-500: #607d8b;
  --md-blue-grey-600: #546e7a;
  --md-blue-grey-700: #455a64;
  --md-blue-grey-800: #37474f;
  --md-blue-grey-900: #263238;
}

/* ../../node_modules/@jupyterlab/apputils/style/toolbar.css */
.jp-Toolbar-item.jp-Toolbar-kernelStatus {
  display: inline-block;
  width: 32px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 16px;
}

/* ../../node_modules/@jupyterlab/apputils/style/base.css */

/* ../../node_modules/@jupyterlab/rendermime/style/base.css */
:root {
  --jp-private-code-span-padding: calc( (var(--jp-code-line-height) - 1) * var(--jp-code-font-size) / 2 );
}
.jp-RenderedText {
  text-align: left;
  padding-left: var(--jp-code-padding);
  line-height: var(--jp-code-line-height);
  font-family: var(--jp-code-font-family);
}
.jp-RenderedText pre,
.jp-RenderedJavaScript pre,
.jp-RenderedHTMLCommon pre {
  color: var(--jp-content-font-color1);
  font-size: var(--jp-code-font-size);
  border: none;
  margin: 0;
  padding: 0;
}
.jp-RenderedText pre a[href]:link {
  text-decoration: none;
  color: var(--jp-content-link-color);
}
.jp-RenderedText pre a[href]:hover {
  text-decoration: underline;
  color: var(--jp-content-link-hover-color, var(--jp-content-link-color));
}
.jp-RenderedText pre a[href]:visited {
  text-decoration: none;
  color: var(--jp-content-link-visited-color, var(--jp-content-link-color));
}
.jp-RenderedText pre .ansi-black-fg {
  color: #3e424d;
}
.jp-RenderedText pre .ansi-red-fg {
  color: #e75c58;
}
.jp-RenderedText pre .ansi-green-fg {
  color: #00a250;
}
.jp-RenderedText pre .ansi-yellow-fg {
  color: #ddb62b;
}
.jp-RenderedText pre .ansi-blue-fg {
  color: #208ffb;
}
.jp-RenderedText pre .ansi-magenta-fg {
  color: #d160c4;
}
.jp-RenderedText pre .ansi-cyan-fg {
  color: #60c6c8;
}
.jp-RenderedText pre .ansi-white-fg {
  color: #c5c1b4;
}
.jp-RenderedText pre .ansi-black-bg {
  background-color: #3e424d;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-red-bg {
  background-color: #e75c58;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-green-bg {
  background-color: #00a250;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-yellow-bg {
  background-color: #ddb62b;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-blue-bg {
  background-color: #208ffb;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-magenta-bg {
  background-color: #d160c4;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-cyan-bg {
  background-color: #60c6c8;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-white-bg {
  background-color: #c5c1b4;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-black-intense-fg {
  color: #282c36;
}
.jp-RenderedText pre .ansi-red-intense-fg {
  color: #b22b31;
}
.jp-RenderedText pre .ansi-green-intense-fg {
  color: #007427;
}
.jp-RenderedText pre .ansi-yellow-intense-fg {
  color: #b27d12;
}
.jp-RenderedText pre .ansi-blue-intense-fg {
  color: #0065ca;
}
.jp-RenderedText pre .ansi-magenta-intense-fg {
  color: #a03196;
}
.jp-RenderedText pre .ansi-cyan-intense-fg {
  color: #258f8f;
}
.jp-RenderedText pre .ansi-white-intense-fg {
  color: #a1a6b2;
}
.jp-RenderedText pre .ansi-black-intense-bg {
  background-color: #282c36;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-red-intense-bg {
  background-color: #b22b31;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-green-intense-bg {
  background-color: #007427;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-yellow-intense-bg {
  background-color: #b27d12;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-blue-intense-bg {
  background-color: #0065ca;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-magenta-intense-bg {
  background-color: #a03196;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-cyan-intense-bg {
  background-color: #258f8f;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-white-intense-bg {
  background-color: #a1a6b2;
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-default-inverse-fg {
  color: var(--jp-ui-inverse-font-color0);
}
.jp-RenderedText pre .ansi-default-inverse-bg {
  background-color: var(--jp-inverse-layout-color0);
  padding: var(--jp-private-code-span-padding) 0;
}
.jp-RenderedText pre .ansi-bold {
  font-weight: bold;
}
.jp-RenderedText pre .ansi-underline {
  text-decoration: underline;
}
.jp-RenderedText[data-mime-type="application/vnd.jupyter.stderr"] {
  background: var(--jp-rendermime-error-background);
  padding-top: var(--jp-code-padding);
}
.jp-RenderedLatex {
  color: var(--jp-content-font-color1);
  font-size: var(--jp-content-font-size1);
  line-height: var(--jp-content-line-height);
}
.jp-OutputArea-output.jp-RenderedLatex {
  padding: var(--jp-code-padding);
  text-align: left;
}
.jp-RenderedHTMLCommon {
  color: var(--jp-content-font-color1);
  font-family: var(--jp-content-font-family);
  font-size: var(--jp-content-font-size1);
  line-height: var(--jp-content-line-height);
  padding-right: 20px;
}
.jp-RenderedHTMLCommon em {
  font-style: italic;
}
.jp-RenderedHTMLCommon strong {
  font-weight: bold;
}
.jp-RenderedHTMLCommon u {
  text-decoration: underline;
}
.jp-RenderedHTMLCommon a:link {
  text-decoration: none;
  color: var(--jp-content-link-color);
}
.jp-RenderedHTMLCommon a:hover {
  text-decoration: underline;
  color: var(--jp-content-link-hover-color, var(--jp-content-link-color));
}
.jp-RenderedHTMLCommon a:visited {
  text-decoration: none;
  color: var(--jp-content-link-visited-color, var(--jp-content-link-color));
}
.jp-RenderedHTMLCommon h1,
.jp-RenderedHTMLCommon h2,
.jp-RenderedHTMLCommon h3,
.jp-RenderedHTMLCommon h4,
.jp-RenderedHTMLCommon h5,
.jp-RenderedHTMLCommon h6 {
  line-height: var(--jp-content-heading-line-height);
  font-weight: var(--jp-content-heading-font-weight);
  font-style: normal;
  margin: var(--jp-content-heading-margin-top) 0 var(--jp-content-heading-margin-bottom) 0;
  scroll-margin-top: var(--jp-content-heading-margin-top);
}
.jp-RenderedHTMLCommon h1:first-child,
.jp-RenderedHTMLCommon h2:first-child,
.jp-RenderedHTMLCommon h3:first-child,
.jp-RenderedHTMLCommon h4:first-child,
.jp-RenderedHTMLCommon h5:first-child,
.jp-RenderedHTMLCommon h6:first-child {
  margin-top: calc(0.5 * var(--jp-content-heading-margin-top));
  scroll-margin-top: calc(0.5 * var(--jp-content-heading-margin-top));
}
.jp-RenderedHTMLCommon h1:last-child,
.jp-RenderedHTMLCommon h2:last-child,
.jp-RenderedHTMLCommon h3:last-child,
.jp-RenderedHTMLCommon h4:last-child,
.jp-RenderedHTMLCommon h5:last-child,
.jp-RenderedHTMLCommon h6:last-child {
  margin-bottom: calc(0.5 * var(--jp-content-heading-margin-bottom));
}
.jp-RenderedHTMLCommon h1 {
  font-size: var(--jp-content-font-size5);
}
.jp-RenderedHTMLCommon h2 {
  font-size: var(--jp-content-font-size4);
}
.jp-RenderedHTMLCommon h3 {
  font-size: var(--jp-content-font-size3);
}
.jp-RenderedHTMLCommon h4 {
  font-size: var(--jp-content-font-size2);
}
.jp-RenderedHTMLCommon h5 {
  font-size: var(--jp-content-font-size1);
}
.jp-RenderedHTMLCommon h6 {
  font-size: var(--jp-content-font-size0);
}
.jp-RenderedHTMLCommon ul:not(.list-inline),
.jp-RenderedHTMLCommon ol:not(.list-inline) {
  padding-left: 2em;
}
.jp-RenderedHTMLCommon ul {
  list-style: disc;
}
.jp-RenderedHTMLCommon ul ul {
  list-style: square;
}
.jp-RenderedHTMLCommon ul ul ul {
  list-style: circle;
}
.jp-RenderedHTMLCommon ol {
  list-style: decimal;
}
.jp-RenderedHTMLCommon ol ol {
  list-style: upper-alpha;
}
.jp-RenderedHTMLCommon ol ol ol {
  list-style: lower-alpha;
}
.jp-RenderedHTMLCommon ol ol ol ol {
  list-style: lower-roman;
}
.jp-RenderedHTMLCommon ol ol ol ol ol {
  list-style: decimal;
}
.jp-RenderedHTMLCommon ol,
.jp-RenderedHTMLCommon ul {
  margin-bottom: 1em;
}
.jp-RenderedHTMLCommon ul ul,
.jp-RenderedHTMLCommon ul ol,
.jp-RenderedHTMLCommon ol ul,
.jp-RenderedHTMLCommon ol ol {
  margin-bottom: 0;
}
.jp-RenderedHTMLCommon hr {
  color: var(--jp-border-color2);
  background-color: var(--jp-border-color1);
  margin-top: 1em;
  margin-bottom: 1em;
}
.jp-RenderedHTMLCommon > pre {
  margin: 1.5em 2em;
}
.jp-RenderedHTMLCommon pre,
.jp-RenderedHTMLCommon code {
  border: 0;
  background-color: var(--jp-layout-color0);
  color: var(--jp-content-font-color1);
  font-family: var(--jp-code-font-family);
  font-size: inherit;
  line-height: var(--jp-code-line-height);
  padding: 0;
  white-space: pre-wrap;
}
.jp-RenderedHTMLCommon :not(pre) > code {
  background-color: var(--jp-layout-color2);
  padding: 1px 5px;
}
.jp-RenderedHTMLCommon table {
  border-collapse: collapse;
  border-spacing: 0;
  border: none;
  color: var(--jp-ui-font-color1);
  font-size: var(--jp-ui-font-size1);
  table-layout: fixed;
  margin-left: auto;
  margin-bottom: 1em;
  margin-right: auto;
}
.jp-RenderedHTMLCommon thead {
  border-bottom: var(--jp-border-width) solid var(--jp-border-color1);
  vertical-align: bottom;
}
.jp-RenderedHTMLCommon td,
.jp-RenderedHTMLCommon th,
.jp-RenderedHTMLCommon tr {
  vertical-align: middle;
  padding: 0.5em;
  line-height: normal;
  white-space: normal;
  max-width: none;
  border: none;
}
.jp-RenderedMarkdown.jp-RenderedHTMLCommon td,
.jp-RenderedMarkdown.jp-RenderedHTMLCommon th {
  max-width: none;
}
:not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td,
:not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon th,
:not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon tr {
  text-align: right;
}
.jp-RenderedHTMLCommon th {
  font-weight: bold;
}
.jp-RenderedHTMLCommon tbody tr:nth-child(odd) {
  background: var(--jp-layout-color0);
}
.jp-RenderedHTMLCommon tbody tr:nth-child(even) {
  background: var(--jp-rendermime-table-row-background);
}
.jp-RenderedHTMLCommon tbody tr:hover {
  background: var(--jp-rendermime-table-row-hover-background);
}
.jp-RenderedHTMLCommon p {
  text-align: left;
  margin: 0;
  margin-bottom: 1em;
}
.jp-RenderedHTMLCommon img {
  -moz-force-broken-image-icon: 1;
}
.jp-RenderedHTMLCommon > img {
  display: block;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 1em;
}
[data-jp-theme-light=false] .jp-RenderedImage img.jp-needs-light-background {
  background-color: var(--jp-inverse-layout-color1);
}
[data-jp-theme-light=true] .jp-RenderedImage img.jp-needs-dark-background {
  background-color: var(--jp-inverse-layout-color1);
}
.jp-RenderedHTMLCommon img,
.jp-RenderedImage img,
.jp-RenderedHTMLCommon svg,
.jp-RenderedSVG svg {
  max-width: 100%;
  height: auto;
}
.jp-RenderedHTMLCommon img.jp-mod-unconfined,
.jp-RenderedImage img.jp-mod-unconfined,
.jp-RenderedHTMLCommon svg.jp-mod-unconfined,
.jp-RenderedSVG svg.jp-mod-unconfined {
  max-width: none;
}
.jp-RenderedHTMLCommon .alert {
  padding: var(--jp-notebook-padding);
  border: var(--jp-border-width) solid transparent;
  border-radius: var(--jp-border-radius);
  margin-bottom: 1em;
}
.jp-RenderedHTMLCommon .alert-info {
  color: var(--jp-info-color0);
  background-color: var(--jp-info-color3);
  border-color: var(--jp-info-color2);
}
.jp-RenderedHTMLCommon .alert-info hr {
  border-color: var(--jp-info-color3);
}
.jp-RenderedHTMLCommon .alert-info > p:last-child,
.jp-RenderedHTMLCommon .alert-info > ul:last-child {
  margin-bottom: 0;
}
.jp-RenderedHTMLCommon .alert-warning {
  color: var(--jp-warn-color0);
  background-color: var(--jp-warn-color3);
  border-color: var(--jp-warn-color2);
}
.jp-RenderedHTMLCommon .alert-warning hr {
  border-color: var(--jp-warn-color3);
}
.jp-RenderedHTMLCommon .alert-warning > p:last-child,
.jp-RenderedHTMLCommon .alert-warning > ul:last-child {
  margin-bottom: 0;
}
.jp-RenderedHTMLCommon .alert-success {
  color: var(--jp-success-color0);
  background-color: var(--jp-success-color3);
  border-color: var(--jp-success-color2);
}
.jp-RenderedHTMLCommon .alert-success hr {
  border-color: var(--jp-success-color3);
}
.jp-RenderedHTMLCommon .alert-success > p:last-child,
.jp-RenderedHTMLCommon .alert-success > ul:last-child {
  margin-bottom: 0;
}
.jp-RenderedHTMLCommon .alert-danger {
  color: var(--jp-error-color0);
  background-color: var(--jp-error-color3);
  border-color: var(--jp-error-color2);
}
.jp-RenderedHTMLCommon .alert-danger hr {
  border-color: var(--jp-error-color3);
}
.jp-RenderedHTMLCommon .alert-danger > p:last-child,
.jp-RenderedHTMLCommon .alert-danger > ul:last-child {
  margin-bottom: 0;
}
.jp-RenderedHTMLCommon blockquote {
  margin: 1em 2em;
  padding: 0 1em;
  border-left: 5px solid var(--jp-border-color2);
}
a.jp-InternalAnchorLink {
  visibility: hidden;
  margin-left: 8px;
  color: var(--md-blue-800, #1565c0);
}
h1:hover .jp-InternalAnchorLink,
h2:hover .jp-InternalAnchorLink,
h3:hover .jp-InternalAnchorLink,
h4:hover .jp-InternalAnchorLink,
h5:hover .jp-InternalAnchorLink,
h6:hover .jp-InternalAnchorLink {
  visibility: visible;
}
.jp-RenderedHTMLCommon kbd {
  background-color: var(--jp-rendermime-table-row-background);
  border: 1px solid var(--jp-border-color0);
  border-bottom-color: var(--jp-border-color2);
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
  display: inline-block;
  font-size: var(--jp-ui-font-size0);
  line-height: 1em;
  padding: 0.2em 0.5em;
}
.jp-RenderedHTMLCommon > *:last-child {
  margin-bottom: 0.5em;
}

/* src/index.css */
.thebe-output-busy-spinner-alt {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  -webkit-animation: thebeoutputspin 2s linear infinite;
  animation: thebeoutputspin 1s linear infinite;
}
.thebe-output-busy-spinner {
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background-color: rgba(250, 250, 250, 0.9);
  z-index: 9999;
  display: block;
}
.thebe-output-busy-spinner::after {
  content: "";
  display: block;
  position: relative;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  border-style: solid;
  border-color: #3498db;
  border-top-color: transparent;
  border-width: 2px;
  border-radius: 50%;
  -webkit-animation: thebeoutputspin 0.8s linear infinite;
  animation: thebeoutputspin 0.8s linear infinite;
}
@-webkit-keyframes thebeoutputspin {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes thebeoutputspin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.thebe-ipywidgets-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 95%;
}
.thebe-ipywidgets-placeholder > pre {
  font-size: 80%;
  font-family: monospace;
  margin: 8px 16px;
}
.thebe-output .jp-OutputArea-child > .lm-Widget {
  overflow: auto;
}
