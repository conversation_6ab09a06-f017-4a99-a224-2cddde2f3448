"use strict";(self.webpackChunkthebe_core=self.webpackChunkthebe_core||[]).push([[9638],{49638:(r,t,n)=>{function e(r,t){return r.skipToEnd(),t.cur=s,"error"}function u(r,t){return r.match(/^HTTP\/\d\.\d/)?(t.cur=c,"keyword"):r.match(/^[A-Z]+/)&&/[ \t]/.test(r.peek())?(t.cur=i,"keyword"):e(r,t)}function c(r,t){var n=r.match(/^\d+/);if(!n)return e(r,t);t.cur=o;var u=Number(n[0]);return u>=100&&u<400?"atom":"error"}function o(r,t){return r.skipToEnd(),t.cur=s,null}function i(r,t){return r.eatWhile(/\S/),t.cur=a,"string.special"}function a(r,t){return r.match(/^HTTP\/\d\.\d$/)?(t.cur=s,"keyword"):e(r,t)}function s(r){return r.sol()&&!r.eat(/[ \t]/)?r.match(/^.*?:/)?"atom":(r.skipToEnd(),"error"):(r.skipToEnd(),"string")}function k(r){return r.skipToEnd(),null}n.r(t),n.d(t,{http:()=>d});const d={name:"http",token:function(r,t){var n=t.cur;return n!=s&&n!=k&&r.eatSpace()?null:n(r,t)},blankLine:function(r){r.cur=k},startState:function(){return{cur:u}}}}}]);
//# sourceMappingURL=9638.thebe-core.min.js.map