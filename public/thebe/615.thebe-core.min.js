"use strict";(self.webpackChunkthebe_core=self.webpackChunkthebe_core||[]).push([[615],{70615:(t,e,a)=>{a.r(e),a.d(e,{troff:()=>h});var r={};function n(t){if(t.eatSpace())return null;var e=t.sol(),a=t.next();if("\\"===a)return t.match("fB")||t.match("fR")||t.match("fI")||t.match("u")||t.match("d")||t.match("%")||t.match("&")?"string":t.match("m[")?(t.skipTo("]"),t.next(),"string"):t.match("s+")||t.match("s-")?(t.eatWhile(/[\d-]/),"string"):t.match("(")||t.match("*(")?(t.eatWhile(/[\w-]/),"string"):"string";if(e&&("."===a||"'"===a)&&t.eat("\\")&&t.eat('"'))return t.skipToEnd(),"comment";if(e&&"."===a){if(t.match("B ")||t.match("I ")||t.match("R "))return"attribute";if(t.match("TH ")||t.match("SH ")||t.match("SS ")||t.match("HP "))return t.skipToEnd(),"quote";if(t.match(/[A-Z]/)&&t.match(/[A-Z]/)||t.match(/[a-z]/)&&t.match(/[a-z]/))return"attribute"}t.eatWhile(/[\w-]/);var n=t.current();return r.hasOwnProperty(n)?r[n]:null}function c(t,e){return(e.tokens[0]||n)(t,e)}const h={name:"troff",startState:function(){return{tokens:[]}},token:function(t,e){return c(t,e)}}}}]);
//# sourceMappingURL=615.thebe-core.min.js.map