/*! For license information please see 746.thebe-lite.min.js.LICENSE.txt */
"use strict";(self.webpackChunkthebe_lite=self.webpackChunkthebe_lite||[]).push([[746],{2746:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var Fi=Object.create,Ke=Object.defineProperty,Bi=Object.getOwnPropertyDescriptor,$i=Object.getOwnPropertyNames,Wi=Object.getPrototypeOf,Hi=Object.prototype.hasOwnProperty,pe=(e,t)=>()=>(e&&(t=e(e=0)),t),L=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ki=(e,t)=>{for(var i in t)Ke(e,i,{get:t[i],enumerable:!0})},Ji=(e,t,i,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of $i(t))!Hi.call(e,n)&&n!==i&&Ke(e,n,{get:()=>t[n],enumerable:!(a=Bi(t,n))||a.enumerable});return e},de=(e,t,i)=>(i=null!=e?Fi(Wi(e)):{},Ji(!t&&e&&e.__esModule?i:Ke(i,"default",{value:e,enumerable:!0}),e)),St=L(((e,t)=>{var i,a;i=e,a=function(e){function t(e,t){let i=0;for(let a of e)if(!1===t(a,i++))return!1;return!0}var i;e.ArrayExt=void 0,function(e){function t(e,t,i=0,a=-1){let n,o=e.length;if(0===o)return-1;i=i<0?Math.max(0,i+o):Math.min(i,o-1),n=(a=a<0?Math.max(0,a+o):Math.min(a,o-1))<i?a+1+(o-i):a-i+1;for(let a=0;a<n;++a){let n=(i+a)%o;if(e[n]===t)return n}return-1}function i(e,t,i=-1,a=0){let n,o=e.length;if(0===o)return-1;n=(i=i<0?Math.max(0,i+o):Math.min(i,o-1))<(a=a<0?Math.max(0,a+o):Math.min(a,o-1))?i+1+(o-a):i-a+1;for(let a=0;a<n;++a){let n=(i-a+o)%o;if(e[n]===t)return n}return-1}function a(e,t,i=0,a=-1){let n,o=e.length;if(0===o)return-1;i=i<0?Math.max(0,i+o):Math.min(i,o-1),n=(a=a<0?Math.max(0,a+o):Math.min(a,o-1))<i?a+1+(o-i):a-i+1;for(let a=0;a<n;++a){let n=(i+a)%o;if(t(e[n],n))return n}return-1}function n(e,t,i=-1,a=0){let n,o=e.length;if(0===o)return-1;n=(i=i<0?Math.max(0,i+o):Math.min(i,o-1))<(a=a<0?Math.max(0,a+o):Math.min(a,o-1))?i+1+(o-a):i-a+1;for(let a=0;a<n;++a){let n=(i-a+o)%o;if(t(e[n],n))return n}return-1}function o(e,t=0,i=-1){let a=e.length;if(!(a<=1))for(t=t<0?Math.max(0,t+a):Math.min(t,a-1),i=i<0?Math.max(0,i+a):Math.min(i,a-1);t<i;){let a=e[t],n=e[i];e[t++]=n,e[i--]=a}}function r(e,t){let i=e.length;if(t<0&&(t+=i),t<0||t>=i)return;let a=e[t];for(let a=t+1;a<i;++a)e[a-1]=e[a];return e.length=i-1,a}e.firstIndexOf=t,e.lastIndexOf=i,e.findFirstIndex=a,e.findLastIndex=n,e.findFirstValue=function(e,t,i=0,n=-1){let o=a(e,t,i,n);return-1!==o?e[o]:void 0},e.findLastValue=function(e,t,i=-1,a=0){let o=n(e,t,i,a);return-1!==o?e[o]:void 0},e.lowerBound=function(e,t,i,a=0,n=-1){let o=e.length;if(0===o)return 0;let r=a=a<0?Math.max(0,a+o):Math.min(a,o-1),l=(n=n<0?Math.max(0,n+o):Math.min(n,o-1))-a+1;for(;l>0;){let a=l>>1,n=r+a;i(e[n],t)<0?(r=n+1,l-=a+1):l=a}return r},e.upperBound=function(e,t,i,a=0,n=-1){let o=e.length;if(0===o)return 0;let r=a=a<0?Math.max(0,a+o):Math.min(a,o-1),l=(n=n<0?Math.max(0,n+o):Math.min(n,o-1))-a+1;for(;l>0;){let a=l>>1,n=r+a;i(e[n],t)>0?l=a:(r=n+1,l-=a+1)}return r},e.shallowEqual=function(e,t,i){if(e===t)return!0;if(e.length!==t.length)return!1;for(let a=0,n=e.length;a<n;++a)if(i?!i(e[a],t[a]):e[a]!==t[a])return!1;return!0},e.slice=function(e,t={}){let{start:i,stop:a,step:n}=t;if(void 0===n&&(n=1),0===n)throw new Error("Slice `step` cannot be zero.");let o,r=e.length;void 0===i?i=n<0?r-1:0:i<0?i=Math.max(i+r,n<0?-1:0):i>=r&&(i=n<0?r-1:r),void 0===a?a=n<0?-1:r:a<0?a=Math.max(a+r,n<0?-1:0):a>=r&&(a=n<0?r-1:r),o=n<0&&a>=i||n>0&&i>=a?0:n<0?Math.floor((a-i+1)/n+1):Math.floor((a-i-1)/n+1);let l=[];for(let t=0;t<o;++t)l[t]=e[i+t*n];return l},e.move=function(e,t,i){let a=e.length;if(a<=1||(t=t<0?Math.max(0,t+a):Math.min(t,a-1))===(i=i<0?Math.max(0,i+a):Math.min(i,a-1)))return;let n=e[t],o=t<i?1:-1;for(let a=t;a!==i;a+=o)e[a]=e[a+o];e[i]=n},e.reverse=o,e.rotate=function(e,t,i=0,a=-1){let n=e.length;if(n<=1||(i=i<0?Math.max(0,i+n):Math.min(i,n-1))>=(a=a<0?Math.max(0,a+n):Math.min(a,n-1)))return;let r=a-i+1;if(t>0?t%=r:t<0&&(t=(t%r+r)%r),0===t)return;let l=i+t;o(e,i,l-1),o(e,l,a),o(e,i,a)},e.fill=function(e,t,i=0,a=-1){let n,o=e.length;if(0!==o){i=i<0?Math.max(0,i+o):Math.min(i,o-1),n=(a=a<0?Math.max(0,a+o):Math.min(a,o-1))<i?a+1+(o-i):a-i+1;for(let a=0;a<n;++a)e[(i+a)%o]=t}},e.insert=function(e,t,i){let a=e.length;t=t<0?Math.max(0,t+a):Math.min(t,a);for(let i=a;i>t;--i)e[i]=e[i-1];e[t]=i},e.removeAt=r,e.removeFirstOf=function(e,i,a=0,n=-1){let o=t(e,i,a,n);return-1!==o&&r(e,o),o},e.removeLastOf=function(e,t,a=-1,n=0){let o=i(e,t,a,n);return-1!==o&&r(e,o),o},e.removeAllOf=function(e,t,i=0,a=-1){let n=e.length;if(0===n)return 0;i=i<0?Math.max(0,i+n):Math.min(i,n-1),a=a<0?Math.max(0,a+n):Math.min(a,n-1);let o=0;for(let r=0;r<n;++r)i<=a&&r>=i&&r<=a&&e[r]===t||a<i&&(r<=a||r>=i)&&e[r]===t?o++:o>0&&(e[r-o]=e[r]);return o>0&&(e.length=n-o),o},e.removeFirstWhere=function(e,t,i=0,n=-1){let o,l=a(e,t,i,n);return-1!==l&&(o=r(e,l)),{index:l,value:o}},e.removeLastWhere=function(e,t,i=-1,a=0){let o,l=n(e,t,i,a);return-1!==l&&(o=r(e,l)),{index:l,value:o}},e.removeAllWhere=function(e,t,i=0,a=-1){let n=e.length;if(0===n)return 0;i=i<0?Math.max(0,i+n):Math.min(i,n-1),a=a<0?Math.max(0,a+n):Math.min(a,n-1);let o=0;for(let r=0;r<n;++r)i<=a&&r>=i&&r<=a&&t(e[r],r)||a<i&&(r<=a||r>=i)&&t(e[r],r)?o++:o>0&&(e[r-o]=e[r]);return o>0&&(e.length=n-o),o}}(e.ArrayExt||(e.ArrayExt={})),(i||(i={})).rangeLength=function(e,t,i){return 0===i?1/0:e>t&&i>0||e<t&&i<0?0:Math.ceil((t-e)/i)},e.StringExt=void 0,function(e){function t(e,t,i=0){let a=new Array(t.length);for(let n=0,o=i,r=t.length;n<r;++n,++o){if(o=e.indexOf(t[n],o),-1===o)return null;a[n]=o}return a}e.findIndices=t,e.matchSumOfSquares=function(e,i,a=0){let n=t(e,i,a);if(!n)return null;let o=0;for(let e=0,t=n.length;e<t;++e){let t=n[e]-a;o+=t*t}return{score:o,indices:n}},e.matchSumOfDeltas=function(e,i,a=0){let n=t(e,i,a);if(!n)return null;let o=0,r=a-1;for(let e=0,t=n.length;e<t;++e){let t=n[e];o+=t-r-1,r=t}return{score:o,indices:n}},e.highlight=function(e,t,i){let a=[],n=0,o=0,r=t.length;for(;n<r;){let l=t[n],s=t[n];for(;++n<r&&t[n]===s+1;)s++;o<l&&a.push(e.slice(o,l)),l<s+1&&a.push(i(e.slice(l,s+1))),o=s+1}return o<e.length&&a.push(e.slice(o)),a},e.cmp=function(e,t){return e<t?-1:e>t?1:0}}(e.StringExt||(e.StringExt={})),e.chain=function*(...e){for(let t of e)yield*t},e.each=function(e,t){let i=0;for(let a of e)if(!1===t(a,i++))return},e.empty=function*(){},e.enumerate=function*(e,t=0){for(let i of e)yield[t++,i]},e.every=t,e.filter=function*(e,t){let i=0;for(let a of e)t(a,i++)&&(yield a)},e.find=function(e,t){let i=0;for(let a of e)if(t(a,i++))return a},e.findIndex=function(e,t){let i=0;for(let a of e)if(t(a,i++))return i-1;return-1},e.map=function*(e,t){let i=0;for(let a of e)yield t(a,i++)},e.max=function(e,t){let i;for(let a of e)void 0!==i?t(a,i)>0&&(i=a):i=a;return i},e.min=function(e,t){let i;for(let a of e)void 0!==i?t(a,i)<0&&(i=a):i=a;return i},e.minmax=function(e,t){let i,a,n=!0;for(let o of e)n?(i=o,a=o,n=!1):t(o,i)<0?i=o:t(o,a)>0&&(a=o);return n?void 0:[i,a]},e.once=function*(e){yield e},e.range=function*(e,t,a){void 0===t?(t=e,e=0,a=1):void 0===a&&(a=1);let n=i.rangeLength(e,t,a);for(let t=0;t<n;t++)yield e+a*t},e.reduce=function(e,t,i){let a=e[Symbol.iterator](),n=0,o=a.next();if(o.done&&void 0===i)throw new TypeError("Reduce of empty iterable with no initial value.");if(o.done)return i;let r,l,s=a.next();if(s.done&&void 0===i)return o.value;if(s.done)return t(i,o.value,n++);for(r=t(void 0===i?o.value:t(i,o.value,n++),s.value,n++);!(l=a.next()).done;)r=t(r,l.value,n++);return r},e.repeat=function*(e,t){for(;0<t--;)yield e},e.retro=function*(e){if("function"==typeof e.retro)yield*e.retro();else for(let t=e.length-1;t>-1;t--)yield e[t]},e.some=function(e,t){let i=0;for(let a of e)if(t(a,i++))return!0;return!1},e.stride=function*(e,t){let i=0;for(let a of e)i++%t==0&&(yield a)},e.take=function*(e,t){if(t<1)return;let i,a=e[Symbol.iterator]();for(;0<t--&&!(i=a.next()).done;)yield i.value},e.toArray=function(e){return Array.from(e)},e.toObject=function(e){let t={};for(let[i,a]of e)t[i]=a;return t},e.topologicSort=function(e){let t=[],i=new Set,a=new Map;for(let t of e)n(t);for(let[e]of a)o(e);return t;function n(e){let[t,i]=e,n=a.get(i);n?n.push(t):a.set(i,[t])}function o(e){if(i.has(e))return;i.add(e);let n=a.get(e);if(n)for(let e of n)o(e);t.push(e)}},e.zip=function*(...e){let i=e.map((e=>e[Symbol.iterator]())),a=i.map((e=>e.next()));for(;t(a,(e=>!e.done));a=i.map((e=>e.next())))yield a.map((e=>e.value))}},"object"==typeof e&&void 0!==t?a(e):"function"==typeof define&&__webpack_require__.amdO?define(["exports"],a):a((i="undefined"!=typeof globalThis?globalThis:i||self).lumino_algorithm={})})),me=L(((e,t)=>{var i,a;i=e,a=function(e){function t(e){let t=0;for(let i=0,a=e.length;i<a;++i)i%4==0&&(t=4294967295*Math.random()>>>0),e[i]=255&t,t>>>=8}e.JSONExt=void 0,function(e){function t(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e}function i(e){return Array.isArray(e)}e.emptyObject=Object.freeze({}),e.emptyArray=Object.freeze([]),e.isPrimitive=t,e.isArray=i,e.isObject=function(e){return!t(e)&&!i(e)},e.deepEqual=function e(a,n){if(a===n)return!0;if(t(a)||t(n))return!1;let o=i(a),r=i(n);return o===r&&(o&&r?function(t,i){if(t===i)return!0;if(t.length!==i.length)return!1;for(let a=0,n=t.length;a<n;++a)if(!e(t[a],i[a]))return!1;return!0}(a,n):function(t,i){if(t===i)return!0;for(let e in t)if(void 0!==t[e]&&!(e in i))return!1;for(let e in i)if(void 0!==i[e]&&!(e in t))return!1;for(let a in t){let n=t[a],o=i[a];if(!(void 0===n&&void 0===o||void 0!==n&&void 0!==o&&e(n,o)))return!1}return!0}(a,n))},e.deepCopy=function e(a){return t(a)?a:i(a)?function(t){let i=new Array(t.length);for(let a=0,n=t.length;a<n;++a)i[a]=e(t[a]);return i}(a):function(t){let i={};for(let a in t){let n=t[a];void 0!==n&&(i[a]=e(n))}return i}(a)}}(e.JSONExt||(e.JSONExt={})),e.Random=void 0,(e.Random||(e.Random={})).getRandomValues=(()=>{let e="undefined"!=typeof window&&(window.crypto||window.msCrypto)||null;return e&&"function"==typeof e.getRandomValues?function(t){return e.getRandomValues(t)}:t})(),e.UUID=void 0,(e.UUID||(e.UUID={})).uuid4=function(e){let t=new Uint8Array(16),i=new Array(256);for(let e=0;e<16;++e)i[e]="0"+e.toString(16);for(let e=16;e<256;++e)i[e]=e.toString(16);return function(){return e(t),t[6]=64|15&t[6],t[8]=128|63&t[8],i[t[0]]+i[t[1]]+i[t[2]]+i[t[3]]+"-"+i[t[4]]+i[t[5]]+"-"+i[t[6]]+i[t[7]]+"-"+i[t[8]]+i[t[9]]+"-"+i[t[10]]+i[t[11]]+i[t[12]]+i[t[13]]+i[t[14]]+i[t[15]]}}(e.Random.getRandomValues),e.MimeData=class{constructor(){this._types=[],this._values=[]}types(){return this._types.slice()}hasData(e){return-1!==this._types.indexOf(e)}getData(e){let t=this._types.indexOf(e);return-1!==t?this._values[t]:void 0}setData(e,t){this.clearData(e),this._types.push(e),this._values.push(t)}clearData(e){let t=this._types.indexOf(e);-1!==t&&(this._types.splice(t,1),this._values.splice(t,1))}clear(){this._types.length=0,this._values.length=0}},e.PromiseDelegate=class{constructor(){this.promise=new Promise(((e,t)=>{this._resolve=e,this._reject=t}))}resolve(e){(0,this._resolve)(e)}reject(e){(0,this._reject)(e)}},e.Token=class{constructor(e,t){this.name=e,this.description=null!=t?t:"",this._tokenStructuralPropertyT=null}}},"object"==typeof e&&void 0!==t?a(e):"function"==typeof define&&__webpack_require__.amdO?define(["exports"],a):a((i="undefined"!=typeof globalThis?globalThis:i||self).lumino_coreutils={})})),zt=L(((e,t)=>{var i,a;i=e,a=function(e,t,i){class a{constructor(e){this.sender=e}connect(e,t){return o.connect(this,e,t)}disconnect(e,t){return o.disconnect(this,e,t)}emit(e){o.emit(this,e)}}var n,o;(n=a||(a={})).disconnectBetween=function(e,t){o.disconnectBetween(e,t)},n.disconnectSender=function(e){o.disconnectSender(e)},n.disconnectReceiver=function(e){o.disconnectReceiver(e)},n.disconnectAll=function(e){o.disconnectAll(e)},n.clearData=function(e){o.disconnectAll(e)},n.getExceptionHandler=function(){return o.exceptionHandler},n.setExceptionHandler=function(e){let t=o.exceptionHandler;return o.exceptionHandler=e,t};class r extends a{constructor(){super(...arguments),this._pending=new i.PromiseDelegate}async*[Symbol.asyncIterator](){let e=this._pending;for(;;)try{let{args:t,next:i}=await e.promise;e=i,yield t}catch{return}}emit(e){let t=this._pending,a=this._pending=new i.PromiseDelegate;t.resolve({args:e,next:a}),super.emit(e)}stop(){this._pending.promise.catch((()=>{})),this._pending.reject("stop"),this._pending=new i.PromiseDelegate}}(function(e){function i(e){let t=n.get(e);if(t&&0!==t.length){for(let e of t){if(!e.signal)continue;let t=e.thisArg||e.slot;e.signal=null,c(o.get(t))}c(t)}}function a(e){let t=o.get(e);if(t&&0!==t.length){for(let e of t){if(!e.signal)continue;let t=e.signal.sender;e.signal=null,c(n.get(t))}c(t)}}e.exceptionHandler=e=>{console.error(e)},e.connect=function(e,t,i){i=i||void 0;let a=n.get(e.sender);if(a||(a=[],n.set(e.sender,a)),s(a,e,t,i))return!1;let r=i||t,l=o.get(r);l||(l=[],o.set(r,l));let p={signal:e,slot:t,thisArg:i};return a.push(p),l.push(p),!0},e.disconnect=function(e,t,i){i=i||void 0;let a=n.get(e.sender);if(!a||0===a.length)return!1;let r=s(a,e,t,i);if(!r)return!1;let l=i||t,p=o.get(l);return r.signal=null,c(a),c(p),!0},e.disconnectBetween=function(e,t){let i=n.get(e);if(!i||0===i.length)return;let a=o.get(t);if(a&&0!==a.length){for(let t of a)t.signal&&t.signal.sender===e&&(t.signal=null);c(i),c(a)}},e.disconnectSender=i,e.disconnectReceiver=a,e.disconnectAll=function(e){i(e),a(e)},e.emit=function(e,t){let i=n.get(e.sender);if(i&&0!==i.length)for(let a=0,n=i.length;a<n;++a){let n=i[a];n.signal===e&&p(n,t)}};let n=new WeakMap,o=new WeakMap,r=new Set,l="function"==typeof requestAnimationFrame?requestAnimationFrame:setImmediate;function s(e,i,a,n){return t.find(e,(e=>e.signal===i&&e.slot===a&&e.thisArg===n))}function p(t,i){let{signal:a,slot:n,thisArg:o}=t;try{n.call(o,a.sender,i)}catch(t){e.exceptionHandler(t)}}function c(e){0===r.size&&l(d),r.add(e)}function d(){r.forEach(m),r.clear()}function m(e){t.ArrayExt.removeAllWhere(e,u)}function u(e){return null===e.signal}})(o||(o={})),e.Signal=a,e.Stream=r},"object"==typeof e&&void 0!==t?a(e,St(),me()):"function"==typeof define&&__webpack_require__.amdO?define(["exports","@lumino/algorithm","@lumino/coreutils"],a):a((i="undefined"!=typeof globalThis?globalThis:i||self).lumino_signaling={},i.lumino_algorithm,i.lumino_coreutils)})),Tt=L((e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ActivityMonitor=void 0;var t=zt();e.ActivityMonitor=class{constructor(e){this._timer=-1,this._timeout=-1,this._isDisposed=!1,this._activityStopped=new t.Signal(this),e.signal.connect(this._onSignalFired,this),this._timeout=e.timeout||1e3}get activityStopped(){return this._activityStopped}get timeout(){return this._timeout}set timeout(e){this._timeout=e}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed||(this._isDisposed=!0,t.Signal.clearData(this))}_onSignalFired(e,t){clearTimeout(this._timer),this._sender=e,this._args=t,this._timer=setTimeout((()=>{this._activityStopped.emit({sender:this._sender,args:this._args})}),this._timeout)}}})),Mt=L((e=>{Object.defineProperty(e,"__esModule",{value:!0})})),At=L((e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.LruCache=void 0,e.LruCache=class{constructor(e={}){this._map=new Map,this._maxSize=(null==e?void 0:e.maxSize)||128}get size(){return this._map.size}clear(){this._map.clear()}get(e){let t=this._map.get(e)||null;return null!=t&&(this._map.delete(e),this._map.set(e,t)),t}set(e,t){this._map.size>=this._maxSize&&this._map.delete(this._map.keys().next().value),this._map.set(e,t)}}})),Ut=L((e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.MarkdownCodeBlocks=void 0,function(e){e.CODE_BLOCK_MARKER="```";let t=[".markdown",".mdown",".mkdn",".md",".mkd",".mdwn",".mdtxt",".mdtext",".text",".txt",".Rmd"];class i{constructor(e){this.startLine=e,this.code="",this.endLine=-1}}e.MarkdownCodeBlock=i,e.isMarkdown=function(e){return t.indexOf(e)>-1},e.findMarkdownCodeBlocks=function(t){if(!t||""===t)return[];let a=t.split("\n"),n=[],o=null;for(let t=0;t<a.length;t++){let r=a[t],l=0===r.indexOf(e.CODE_BLOCK_MARKER),s=null!=o;if(l||s)if(s)o&&(l?(o.endLine=t-1,n.push(o),o=null):o.code+=r+"\n");else{o=new i(t);let a=r.indexOf(e.CODE_BLOCK_MARKER),l=r.lastIndexOf(e.CODE_BLOCK_MARKER);a!==l&&(o.code=r.substring(a+e.CODE_BLOCK_MARKER.length,l),o.endLine=t,n.push(o),o=null)}}return n}}(t||(e.MarkdownCodeBlocks=t={}))})),Bt=L(((e,t)=>{function i(e){return!("number"!=typeof e&&!/^0x[0-9a-f]+$/i.test(e))||/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(e)}function a(e,t){return"constructor"===t&&"function"==typeof e[t]||"__proto__"===t}t.exports=function(e,t){t||(t={});var n={bools:{},strings:{},unknownFn:null};"function"==typeof t.unknown&&(n.unknownFn=t.unknown),"boolean"==typeof t.boolean&&t.boolean?n.allBools=!0:[].concat(t.boolean).filter(Boolean).forEach((function(e){n.bools[e]=!0}));var o={};function r(e){return o[e].some((function(e){return n.bools[e]}))}Object.keys(t.alias||{}).forEach((function(e){o[e]=[].concat(t.alias[e]),o[e].forEach((function(t){o[t]=[e].concat(o[e].filter((function(e){return t!==e})))}))})),[].concat(t.string).filter(Boolean).forEach((function(e){n.strings[e]=!0,o[e]&&[].concat(o[e]).forEach((function(e){n.strings[e]=!0}))}));var l=t.default||{},s={_:[]};function p(e,t,i){for(var o=e,r=0;r<t.length-1;r++){var l=t[r];if(a(o,l))return;void 0===o[l]&&(o[l]={}),(o[l]===Object.prototype||o[l]===Number.prototype||o[l]===String.prototype)&&(o[l]={}),o[l]===Array.prototype&&(o[l]=[]),o=o[l]}var s=t[t.length-1];a(o,s)||((o===Object.prototype||o===Number.prototype||o===String.prototype)&&(o={}),o===Array.prototype&&(o=[]),void 0===o[s]||n.bools[s]||"boolean"==typeof o[s]?o[s]=i:Array.isArray(o[s])?o[s].push(i):o[s]=[o[s],i])}function c(e,t,a){if(!a||!n.unknownFn||function(e,t){return n.allBools&&/^--[^=]+$/.test(t)||n.strings[e]||n.bools[e]||o[e]}(e,a)||!1!==n.unknownFn(a)){var r=!n.strings[e]&&i(t)?Number(t):t;p(s,e.split("."),r),(o[e]||[]).forEach((function(e){p(s,e.split("."),r)}))}}Object.keys(n.bools).forEach((function(e){c(e,void 0!==l[e]&&l[e])}));var d=[];-1!==e.indexOf("--")&&(d=e.slice(e.indexOf("--")+1),e=e.slice(0,e.indexOf("--")));for(var m=0;m<e.length;m++){var u,f,h=e[m];if(/^--.+=/.test(h)){var v=h.match(/^--([^=]+)=([\s\S]*)$/);u=v[1];var g=v[2];n.bools[u]&&(g="false"!==g),c(u,g,h)}else if(/^--no-.+/.test(h))c(u=h.match(/^--no-(.+)/)[1],!1,h);else if(/^--.+/.test(h))u=h.match(/^--(.+)/)[1],void 0===(f=e[m+1])||/^(-|--)[^-]/.test(f)||n.bools[u]||n.allBools||o[u]&&r(u)?/^(true|false)$/.test(f)?(c(u,"true"===f,h),m+=1):c(u,!n.strings[u]||"",h):(c(u,f,h),m+=1);else if(/^-[^-]+/.test(h)){for(var x=h.slice(1,-1).split(""),b=!1,y=0;y<x.length;y++)if("-"!==(f=h.slice(y+2))){if(/[A-Za-z]/.test(x[y])&&"="===f[0]){c(x[y],f.slice(1),h),b=!0;break}if(/[A-Za-z]/.test(x[y])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(f)){c(x[y],f,h),b=!0;break}if(x[y+1]&&x[y+1].match(/\W/)){c(x[y],h.slice(y+2),h),b=!0;break}c(x[y],!n.strings[x[y]]||"",h)}else c(x[y],f,h);u=h.slice(-1)[0],!b&&"-"!==u&&(!e[m+1]||/^(-|--)[^-]/.test(e[m+1])||n.bools[u]||o[u]&&r(u)?e[m+1]&&/^(true|false)$/.test(e[m+1])?(c(u,"true"===e[m+1],h),m+=1):c(u,!n.strings[u]||"",h):(c(u,e[m+1],h),m+=1))}else if((!n.unknownFn||!1!==n.unknownFn(h))&&s._.push(n.strings._||!i(h)?h:Number(h)),t.stopEarly){s._.push.apply(s._,e.slice(m+1));break}}return Object.keys(l).forEach((function(e){(function(e,t){var i=e;return t.slice(0,-1).forEach((function(e){i=i[e]||{}})),t[t.length-1]in i})(s,e.split("."))||(p(s,e.split("."),l[e]),(o[e]||[]).forEach((function(t){p(s,t.split("."),l[e])})))})),t["--"]?s["--"]=d.slice():d.forEach((function(e){s._.push(e)})),s}})),ze=L(((e,t)=>{function i(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function a(e,t){for(var i,a="",n=0,o=-1,r=0,l=0;l<=e.length;++l){if(l<e.length)i=e.charCodeAt(l);else{if(47===i)break;i=47}if(47===i){if(o!==l-1&&1!==r)if(o!==l-1&&2===r){if(a.length<2||2!==n||46!==a.charCodeAt(a.length-1)||46!==a.charCodeAt(a.length-2))if(a.length>2){var s=a.lastIndexOf("/");if(s!==a.length-1){-1===s?(a="",n=0):n=(a=a.slice(0,s)).length-1-a.lastIndexOf("/"),o=l,r=0;continue}}else if(2===a.length||1===a.length){a="",n=0,o=l,r=0;continue}t&&(a.length>0?a+="/..":a="..",n=2)}else a.length>0?a+="/"+e.slice(o+1,l):a=e.slice(o+1,l),n=l-o-1;o=l,r=0}else 46===i&&-1!==r?++r:r=-1}return a}var n={resolve:function(){for(var e,t="",n=!1,o=arguments.length-1;o>=-1&&!n;o--){var r;o>=0?r=arguments[o]:(void 0===e&&(e=process.cwd()),r=e),i(r),0!==r.length&&(t=r+"/"+t,n=47===r.charCodeAt(0))}return t=a(t,!n),n?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(e){if(i(e),0===e.length)return".";var t=47===e.charCodeAt(0),n=47===e.charCodeAt(e.length-1);return 0===(e=a(e,!t)).length&&!t&&(e="."),e.length>0&&n&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return i(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var a=arguments[t];i(a),a.length>0&&(void 0===e?e=a:e+="/"+a)}return void 0===e?".":n.normalize(e)},relative:function(e,t){if(i(e),i(t),e===t||(e=n.resolve(e))===(t=n.resolve(t)))return"";for(var a=1;a<e.length&&47===e.charCodeAt(a);++a);for(var o=e.length,r=o-a,l=1;l<t.length&&47===t.charCodeAt(l);++l);for(var s=t.length-l,p=r<s?r:s,c=-1,d=0;d<=p;++d){if(d===p){if(s>p){if(47===t.charCodeAt(l+d))return t.slice(l+d+1);if(0===d)return t.slice(l+d)}else r>p&&(47===e.charCodeAt(a+d)?c=d:0===d&&(c=0));break}var m=e.charCodeAt(a+d);if(m!==t.charCodeAt(l+d))break;47===m&&(c=d)}var u="";for(d=a+c+1;d<=o;++d)(d===o||47===e.charCodeAt(d))&&(0===u.length?u+="..":u+="/..");return u.length>0?u+t.slice(l+c):(l+=c,47===t.charCodeAt(l)&&++l,t.slice(l))},_makeLong:function(e){return e},dirname:function(e){if(i(e),0===e.length)return".";for(var t=e.charCodeAt(0),a=47===t,n=-1,o=!0,r=e.length-1;r>=1;--r)if(47===(t=e.charCodeAt(r))){if(!o){n=r;break}}else o=!1;return-1===n?a?"/":".":a&&1===n?"//":e.slice(0,n)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');i(e);var a,n=0,o=-1,r=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var l=t.length-1,s=-1;for(a=e.length-1;a>=0;--a){var p=e.charCodeAt(a);if(47===p){if(!r){n=a+1;break}}else-1===s&&(r=!1,s=a+1),l>=0&&(p===t.charCodeAt(l)?-1==--l&&(o=a):(l=-1,o=s))}return n===o?o=s:-1===o&&(o=e.length),e.slice(n,o)}for(a=e.length-1;a>=0;--a)if(47===e.charCodeAt(a)){if(!r){n=a+1;break}}else-1===o&&(r=!1,o=a+1);return-1===o?"":e.slice(n,o)},extname:function(e){i(e);for(var t=-1,a=0,n=-1,o=!0,r=0,l=e.length-1;l>=0;--l){var s=e.charCodeAt(l);if(47!==s)-1===n&&(o=!1,n=l+1),46===s?-1===t?t=l:1!==r&&(r=1):-1!==t&&(r=-1);else if(!o){a=l+1;break}}return-1===t||-1===n||0===r||1===r&&t===n-1&&t===a+1?"":e.slice(t,n)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var i=t.dir||t.root,a=t.base||(t.name||"")+(t.ext||"");return i?i===t.root?i+a:i+"/"+a:a}(0,e)},parse:function(e){i(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var a,n=e.charCodeAt(0),o=47===n;o?(t.root="/",a=1):a=0;for(var r=-1,l=0,s=-1,p=!0,c=e.length-1,d=0;c>=a;--c)if(47!==(n=e.charCodeAt(c)))-1===s&&(p=!1,s=c+1),46===n?-1===r?r=c:1!==d&&(d=1):-1!==r&&(d=-1);else if(!p){l=c+1;break}return-1===r||-1===s||0===d||1===d&&r===s-1&&r===l+1?-1!==s&&(t.base=t.name=0===l&&o?e.slice(1,s):e.slice(l,s)):(0===l&&o?(t.name=e.slice(1,r),t.base=e.slice(1,s)):(t.name=e.slice(l,r),t.base=e.slice(l,s)),t.ext=e.slice(r,s)),l>0?t.dir=e.slice(0,l-1):o&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,t.exports=n})),Kt=L(((e,t)=>{t.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}})),Yt=L((e=>{var t=Object.prototype.hasOwnProperty;function i(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch{return null}}function a(e){try{return encodeURIComponent(e)}catch{return null}}e.stringify=function(e,i){i=i||"";var n,o,r=[];for(o in"string"!=typeof i&&(i="?"),e)if(t.call(e,o)){if(!(n=e[o])&&(null==n||isNaN(n))&&(n=""),o=a(o),n=a(n),null===o||null===n)continue;r.push(o+"="+n)}return r.length?i+r.join("&"):""},e.parse=function(e){for(var t,a=/([^=?#&]+)=?([^&]*)/g,n={};t=a.exec(e);){var o=i(t[1]),r=i(t[2]);null===o||null===r||o in n||(n[o]=r)}return n}})),ni=L(((e,t)=>{var i=Kt(),a=Yt(),n=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,o=/[\n\r\t]/g,r=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,l=/:\d+$/,s=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,p=/^[a-zA-Z]:/;function c(e){return(e||"").toString().replace(n,"")}var d=[["#","hash"],["?","query"],function(e,t){return f(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],m={hash:1,query:1};function u(e){var t,i=("undefined"!=typeof window?window:void 0!==__webpack_require__.g?__webpack_require__.g:"undefined"!=typeof self?self:{}).location||{},a={},n=typeof(e=e||i);if("blob:"===e.protocol)a=new v(unescape(e.pathname),{});else if("string"===n)for(t in a=new v(e,{}),m)delete a[t];else if("object"===n){for(t in e)t in m||(a[t]=e[t]);void 0===a.slashes&&(a.slashes=r.test(e.href))}return a}function f(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function h(e,t){e=(e=c(e)).replace(o,""),t=t||{};var i,a=s.exec(e),n=a[1]?a[1].toLowerCase():"",r=!!a[2],l=!!a[3],p=0;return r?l?(i=a[2]+a[3]+a[4],p=a[2].length+a[3].length):(i=a[2]+a[4],p=a[2].length):l?(i=a[3]+a[4],p=a[3].length):i=a[4],"file:"===n?p>=2&&(i=i.slice(2)):f(n)?i=a[4]:n?r&&(i=i.slice(2)):p>=2&&f(t.protocol)&&(i=a[4]),{protocol:n,slashes:r||f(n),slashesCount:p,rest:i}}function v(e,t,n){if(e=(e=c(e)).replace(o,""),!(this instanceof v))return new v(e,t,n);var r,l,s,m,g,x,b=d.slice(),y=typeof t,w=this,_=0;for("object"!==y&&"string"!==y&&(n=t,t=null),n&&"function"!=typeof n&&(n=a.parse),r=!(l=h(e||"",t=u(t))).protocol&&!l.slashes,w.slashes=l.slashes||r&&t.slashes,w.protocol=l.protocol||t.protocol||"",e=l.rest,("file:"===l.protocol&&(2!==l.slashesCount||p.test(e))||!l.slashes&&(l.protocol||l.slashesCount<2||!f(w.protocol)))&&(b[3]=[/(.*)/,"pathname"]);_<b.length;_++)"function"!=typeof(m=b[_])?(s=m[0],x=m[1],s!=s?w[x]=e:"string"==typeof s?~(g="@"===s?e.lastIndexOf(s):e.indexOf(s))&&("number"==typeof m[2]?(w[x]=e.slice(0,g),e=e.slice(g+m[2])):(w[x]=e.slice(g),e=e.slice(0,g))):(g=s.exec(e))&&(w[x]=g[1],e=e.slice(0,g.index)),w[x]=w[x]||r&&m[3]&&t[x]||"",m[4]&&(w[x]=w[x].toLowerCase())):e=m(e,w);n&&(w.query=n(w.query)),r&&t.slashes&&"/"!==w.pathname.charAt(0)&&(""!==w.pathname||""!==t.pathname)&&(w.pathname=function(e,t){if(""===e)return t;for(var i=(t||"/").split("/").slice(0,-1).concat(e.split("/")),a=i.length,n=i[a-1],o=!1,r=0;a--;)"."===i[a]?i.splice(a,1):".."===i[a]?(i.splice(a,1),r++):r&&(0===a&&(o=!0),i.splice(a,1),r--);return o&&i.unshift(""),("."===n||".."===n)&&i.push(""),i.join("/")}(w.pathname,t.pathname)),"/"!==w.pathname.charAt(0)&&f(w.protocol)&&(w.pathname="/"+w.pathname),i(w.port,w.protocol)||(w.host=w.hostname,w.port=""),w.username=w.password="",w.auth&&(~(g=w.auth.indexOf(":"))?(w.username=w.auth.slice(0,g),w.username=encodeURIComponent(decodeURIComponent(w.username)),w.password=w.auth.slice(g+1),w.password=encodeURIComponent(decodeURIComponent(w.password))):w.username=encodeURIComponent(decodeURIComponent(w.auth)),w.auth=w.password?w.username+":"+w.password:w.username),w.origin="file:"!==w.protocol&&f(w.protocol)&&w.host?w.protocol+"//"+w.host:"null",w.href=w.toString()}v.prototype={set:function(e,t,n){var o=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(n||a.parse)(t)),o[e]=t;break;case"port":o[e]=t,i(t,o.protocol)?t&&(o.host=o.hostname+":"+t):(o.host=o.hostname,o[e]="");break;case"hostname":o[e]=t,o.port&&(t+=":"+o.port),o.host=t;break;case"host":o[e]=t,l.test(t)?(t=t.split(":"),o.port=t.pop(),o.hostname=t.join(":")):(o.hostname=t,o.port="");break;case"protocol":o.protocol=t.toLowerCase(),o.slashes=!n;break;case"pathname":case"hash":if(t){var r="pathname"===e?"/":"#";o[e]=t.charAt(0)!==r?r+t:t}else o[e]=t;break;case"username":case"password":o[e]=encodeURIComponent(t);break;case"auth":var s=t.indexOf(":");~s?(o.username=t.slice(0,s),o.username=encodeURIComponent(decodeURIComponent(o.username)),o.password=t.slice(s+1),o.password=encodeURIComponent(decodeURIComponent(o.password))):o.username=encodeURIComponent(decodeURIComponent(t))}for(var p=0;p<d.length;p++){var c=d[p];c[4]&&(o[c[1]]=o[c[1]].toLowerCase())}return o.auth=o.password?o.username+":"+o.password:o.username,o.origin="file:"!==o.protocol&&f(o.protocol)&&o.host?o.protocol+"//"+o.host:"null",o.href=o.toString(),o},toString:function(e){(!e||"function"!=typeof e)&&(e=a.stringify);var t,i=this,n=i.host,o=i.protocol;o&&":"!==o.charAt(o.length-1)&&(o+=":");var r=o+(i.protocol&&i.slashes||f(i.protocol)?"//":"");return i.username?(r+=i.username,i.password&&(r+=":"+i.password),r+="@"):i.password?(r+=":"+i.password,r+="@"):"file:"!==i.protocol&&f(i.protocol)&&!n&&"/"!==i.pathname&&(r+="@"),(":"===n[n.length-1]||l.test(i.hostname)&&!i.port)&&(n+=":"),r+=n+i.pathname,(t="object"==typeof i.query?e(i.query):i.query)&&(r+="?"!==t.charAt(0)?"?"+t:t),i.hash&&(r+=i.hash),r}},v.extractProtocol=h,v.location=u,v.trimLeft=c,v.qs=a,t.exports=v})),st=L((e=>{var t=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.URLExt=void 0;var i,a=ze(),n=t(ni());!function(e){function t(e){if("undefined"!=typeof document&&document){let t=document.createElement("a");return t.href=e,t}return(0,n.default)(e)}function i(...e){let t=(0,n.default)(e[0],{}),i=""===t.protocol&&t.slashes;i&&(t=(0,n.default)(e[0],"https:"+e[0]));let o=`${i?"":t.protocol}${t.slashes?"//":""}${t.auth}${t.auth?"@":""}${t.host}`,r=a.posix.join(`${o&&"/"!==t.pathname[0]?"/":""}${t.pathname}`,...e.slice(1));return`${o}${"."===r?"":r}`}e.parse=t,e.getHostName=function(e){return(0,n.default)(e).hostname},e.normalize=function(e){return e&&t(e).toString()},e.join=i,e.encodeParts=function(e){return i(...e.split("/").map(encodeURIComponent))},e.objectToQueryString=function(e){let t=Object.keys(e).filter((e=>e.length>0));return t.length?"?"+t.map((t=>{let i=encodeURIComponent(String(e[t]));return t+(i?"="+i:"")})).join("&"):""},e.queryStringToObject=function(e){return e.replace(/^\?/,"").split("&").reduce(((e,t)=>{let[i,a]=t.split("=");return i.length>0&&(e[i]=decodeURIComponent(a||"")),e}),{})},e.isLocal=function(e,i=!1){let{protocol:a}=t(e);return(!a||0!==e.toLowerCase().indexOf(a))&&(i?0!==e.indexOf("//"):0!==e.indexOf("/"))}}(i||(e.URLExt=i={}))})),oi=L(((exports,module)=>{var __importDefault=exports&&exports.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.PageConfig=void 0;var coreutils_1=me(),minimist_1=__importDefault(Bt()),url_1=st(),PageConfig;(function(PageConfig){function getOption(name){if(configData)return configData[name]||getBodyData(name);configData=Object.create(null);let found=!1;if("undefined"!=typeof document&&document){let e=document.getElementById("jupyter-config-data");e&&(configData=JSON.parse(e.textContent||""),found=!0)}if(!found&&"undefined"!=typeof process&&process.argv)try{let cli=(0,minimist_1.default)(process.argv.slice(2)),path=ze(),fullPath="";"jupyter-config-data"in cli?fullPath=path.resolve(cli["jupyter-config-data"]):"JUPYTER_CONFIG_DATA"in{}&&(fullPath=path.resolve({}.JUPYTER_CONFIG_DATA)),fullPath&&(configData=eval("require")(fullPath))}catch(e){console.error(e)}if(coreutils_1.JSONExt.isObject(configData))for(let e in configData)"string"!=typeof configData[e]&&(configData[e]=JSON.stringify(configData[e]));else configData=Object.create(null);return configData[name]||getBodyData(name)}function setOption(e,t){let i=getOption(e);return configData[e]=t,i}function getBaseUrl(){return url_1.URLExt.normalize(getOption("baseUrl")||"/")}function getTreeUrl(){return url_1.URLExt.join(getBaseUrl(),getOption("treeUrl"))}function getShareUrl(){return url_1.URLExt.normalize(getOption("shareUrl")||getBaseUrl())}function getTreeShareUrl(){return url_1.URLExt.normalize(url_1.URLExt.join(getShareUrl(),getOption("treeUrl")))}function getUrl(e){var t,i,a,n;let o=e.toShare?getShareUrl():getBaseUrl(),r=null!==(t=e.mode)&&void 0!==t?t:getOption("mode"),l=null!==(i=e.workspace)&&void 0!==i?i:getOption("workspace"),s="single-document"===r?"doc":"lab";o=url_1.URLExt.join(o,s),l!==PageConfig.defaultWorkspace&&(o=url_1.URLExt.join(o,"workspaces",encodeURIComponent(null!==(a=getOption("workspace"))&&void 0!==a?a:PageConfig.defaultWorkspace)));let p=null!==(n=e.treePath)&&void 0!==n?n:getOption("treePath");return p&&(o=url_1.URLExt.join(o,"tree",url_1.URLExt.encodeParts(p))),o}function getWsUrl(e){let t=getOption("wsUrl");if(!t){if(0!==(e=e?url_1.URLExt.normalize(e):getBaseUrl()).indexOf("http"))return"";t="ws"+e.slice(4)}return url_1.URLExt.normalize(t)}function getNBConvertURL({path:e,format:t,download:i}){let a=url_1.URLExt.encodeParts(e),n=url_1.URLExt.join(getBaseUrl(),"nbconvert",t,a);return i?n+"?download=true":n}function getToken(){return getOption("token")||getBodyData("jupyterApiToken")}function getNotebookVersion(){let e=getOption("notebookVersion");return""===e?[0,0,0]:JSON.parse(e)}PageConfig.getOption=getOption,PageConfig.setOption=setOption,PageConfig.getBaseUrl=getBaseUrl,PageConfig.getTreeUrl=getTreeUrl,PageConfig.getShareUrl=getShareUrl,PageConfig.getTreeShareUrl=getTreeShareUrl,PageConfig.getUrl=getUrl,PageConfig.defaultWorkspace="default",PageConfig.getWsUrl=getWsUrl,PageConfig.getNBConvertURL=getNBConvertURL,PageConfig.getToken=getToken,PageConfig.getNotebookVersion=getNotebookVersion;let configData=null,Extension;function getBodyData(e){if("undefined"==typeof document||!document.body)return"";let t=document.body.dataset[e];return void 0===t?"":decodeURIComponent(t)}!function(e){function t(e){try{let t=getOption(e);if(t)return JSON.parse(t)}catch(t){console.warn(`Unable to parse ${e}.`,t)}return[]}e.deferred=t("deferredExtensions"),e.disabled=t("disabledExtensions"),e.isDeferred=function(t){let i=t.indexOf(":"),a="";return-1!==i&&(a=t.slice(0,i)),e.deferred.some((e=>e===t||a&&e===a))},e.isDisabled=function(t){let i=t.indexOf(":"),a="";return-1!==i&&(a=t.slice(0,i)),e.disabled.some((e=>e===t||a&&e===a))}}(Extension=PageConfig.Extension||(PageConfig.Extension={}))})(PageConfig||(exports.PageConfig=PageConfig={}))})),ri=L((e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PathExt=void 0;var t,i=ze();!function(e){function t(e){return 0===e.indexOf("/")&&(e=e.slice(1)),e}e.join=function(...e){let a=i.posix.join(...e);return"."===a?"":t(a)},e.joinWithLeadingSlash=function(...e){let t=i.posix.join(...e);return"."===t?"":t},e.basename=function(e,t){return i.posix.basename(e,t)},e.dirname=function(e){let a=t(i.posix.dirname(e));return"."===a?"":a},e.extname=function(e){return i.posix.extname(e)},e.normalize=function(e){return""===e?"":t(i.posix.normalize(e))},e.resolve=function(...e){return t(i.posix.resolve(...e))},e.relative=function(e,a){return t(i.posix.relative(e,a))},e.normalizeExtension=function(e){return e.length>0&&0!==e.indexOf(".")&&(e=`.${e}`),e},e.removeSlash=t}(t||(e.PathExt=t={}))})),li=L((e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.signalToPromise=void 0;var t=me();e.signalToPromise=function(e,i){let a=new t.PromiseDelegate;function n(){e.disconnect(o)}function o(e,t){n(),a.resolve([e,t])}return e.connect(o),(null!=i?i:0)>0&&setTimeout((()=>{n(),a.reject(`Signal not emitted within ${i} ms.`)}),i),a.promise}})),pi=L((e=>{var t,i;Object.defineProperty(e,"__esModule",{value:!0}),e.Text=void 0,(i=t||(e.Text=t={})).jsIndexToCharIndex=function(e,t){return e},i.charIndexToJsIndex=function(e,t){return e},i.camelCase=function(e,t=!1){return e.replace(/^(\w)|[\s-_:]+(\w)/g,(function(e,i,a){return a?a.toUpperCase():t?i.toUpperCase():i.toLowerCase()}))},i.titleCase=function(e){return(e||"").toLowerCase().split(" ").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" ")}})),mi=L((e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Time=void 0;var t,i,a=[{name:"years",milliseconds:31536e6},{name:"months",milliseconds:2592e6},{name:"days",milliseconds:864e5},{name:"hours",milliseconds:36e5},{name:"minutes",milliseconds:6e4},{name:"seconds",milliseconds:1e3}];(i=t||(e.Time=t={})).formatHuman=function(e,t="long"){let i=document.documentElement.lang||"en",n=new Intl.RelativeTimeFormat(i,{numeric:"auto",style:t}),o=new Date(e).getTime()-Date.now();for(let e of a){let t=Math.ceil(o/e.milliseconds);if(0!==t)return n.format(t,e.name)}return n.format(0,"seconds")},i.format=function(e){let t=document.documentElement.lang||"en";return new Intl.DateTimeFormat(t,{dateStyle:"short",timeStyle:"short"}).format(new Date(e))}})),be=L((e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,i,a){void 0===a&&(a=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,a,n)}:function(e,t,i,a){void 0===a&&(a=i),e[a]=t[i]}),i=e&&e.__exportStar||function(e,i){for(var a in e)"default"!==a&&!Object.prototype.hasOwnProperty.call(i,a)&&t(i,e,a)};Object.defineProperty(e,"__esModule",{value:!0}),i(Tt(),e),i(Mt(),e),i(At(),e),i(Ut(),e),i(oi(),e),i(ri(),e),i(li(),e),i(pi(),e),i(mi(),e),i(st(),e)})),ui=L(((e,t)=>{function i(){this._types=Object.create(null),this._extensions=Object.create(null);for(let e=0;e<arguments.length;e++)this.define(arguments[e]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}i.prototype.define=function(e,t){for(let i in e){let a=e[i].map((function(e){return e.toLowerCase()}));i=i.toLowerCase();for(let e=0;e<a.length;e++){let n=a[e];if("*"!==n[0]){if(!t&&n in this._types)throw new Error('Attempt to change mapping for "'+n+'" extension from "'+this._types[n]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+n+'" from the list of extensions for "'+i+'".');this._types[n]=i}}if(t||!this._extensions[i]){let e=a[0];this._extensions[i]="*"!==e[0]?e:e.substr(1)}}},i.prototype.getType=function(e){let t=(e=String(e)).replace(/^.*[/\\]/,"").toLowerCase(),i=t.replace(/^.*\./,"").toLowerCase(),a=t.length<e.length;return(i.length<t.length-1||!a)&&this._types[i]||null},i.prototype.getExtension=function(e){return(e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1)&&this._extensions[e.toLowerCase()]||null},t.exports=i})),vi=L(((e,t)=>{t.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}})),xi=L(((e,t)=>{t.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}})),yi=L(((e,t)=>{var i=ui();t.exports=new i(vi(),xi())})),bi,_i,rt,bn,G,re,_n,lt=pe((()=>{var e;bi=de(be()),_i=de(yi()),rt=de(me()),bn=new rt.Token("@jupyterlite/contents:IContents"),(e=G||(G={})).JSON="application/json",e.PLAIN_TEXT="text/plain",e.OCTET_STREAM="octet/stream",function(e){let t=JSON.parse(bi.PageConfig.getOption("fileTypes")||"{}");e.getType=function(e,i=null){e=e.toLowerCase();for(let i of Object.values(t))for(let t of i.extensions||[])if(t===e&&i.mimeTypes&&i.mimeTypes.length)return i.mimeTypes[0];return _i.default.getType(e)||i||G.OCTET_STREAM},e.hasFormat=function(e,i){e=e.toLowerCase();for(let a of Object.values(t))if(a.fileFormat===i)for(let t of a.extensions||[])if(t===e)return!0;return!1}}(re||(re={})),_n=new rt.Token("@jupyterlite/contents:IBroadcastChannelWrapper")})),le,ee,Ci,ki,Oi,ct,qe,Pi=pe((()=>{le=de(be()),ee=de(be()),lt(),Ci=de(me()),ki="JupyterLite Storage",Oi=5,ct=class{constructor(e){this.reduceBytesToString=(e,t)=>e+String.fromCharCode(t),this._serverContents=new Map,this._storageName=ki,this._storageDrivers=null,this._localforage=e.localforage,this._storageName=e.storageName||ki,this._storageDrivers=e.storageDrivers||null,this._ready=new Ci.PromiseDelegate}async initialize(){await this.initStorage(),this._ready.resolve(void 0)}async initStorage(){this._storage=this.createDefaultStorage(),this._counters=this.createDefaultCounters(),this._checkpoints=this.createDefaultCheckpoints()}get ready(){return this._ready.promise}get storage(){return this.ready.then((()=>this._storage))}get counters(){return this.ready.then((()=>this._counters))}get checkpoints(){return this.ready.then((()=>this._checkpoints))}get defaultStorageOptions(){let e=this._storageDrivers&&this._storageDrivers.length?this._storageDrivers:null;return{version:1,name:this._storageName,...e?{driver:e}:{}}}createDefaultStorage(){return this._localforage.createInstance({description:"Offline Storage for Notebooks and Files",storeName:"files",...this.defaultStorageOptions})}createDefaultCounters(){return this._localforage.createInstance({description:"Store the current file suffix counters",storeName:"counters",...this.defaultStorageOptions})}createDefaultCheckpoints(){return this._localforage.createInstance({description:"Offline Storage for Checkpoints",storeName:"checkpoints",...this.defaultStorageOptions})}async newUntitled(e){var t,i,a;let n,o=null!==(t=null==e?void 0:e.path)&&void 0!==t?t:"",r=null!==(i=null==e?void 0:e.type)&&void 0!==i?i:"notebook",l=(new Date).toISOString(),s=ee.PathExt.dirname(o),p=ee.PathExt.basename(o),c=ee.PathExt.extname(o),d=await this.get(s),m="";switch(o&&!c&&d?(s=`${o}/`,m=""):s&&p?(s=`${s}/`,m=p):(s="",m=o),r){case"directory":m=`Untitled Folder${await this._incrementCounter("directory")||""}`,n={name:m,path:`${s}${m}`,last_modified:l,created:l,format:"json",mimetype:"",content:null,size:0,writable:!0,type:"directory"};break;case"notebook":{let e=await this._incrementCounter("notebook");m=m||`Untitled${e||""}.ipynb`,n={name:m,path:`${s}${m}`,last_modified:l,created:l,format:"json",mimetype:G.JSON,content:qe.EMPTY_NB,size:JSON.stringify(qe.EMPTY_NB).length,writable:!0,type:"notebook"};break}default:{let t,i=null!==(a=null==e?void 0:e.ext)&&void 0!==a?a:".txt",o=await this._incrementCounter("file"),r=re.getType(i)||G.OCTET_STREAM;t=re.hasFormat(i,"text")||-1!==r.indexOf("text")?"text":-1!==i.indexOf("json")||-1!==i.indexOf("ipynb")?"json":"base64",m=m||`untitled${o||""}${i}`,n={name:m,path:`${s}${m}`,last_modified:l,created:l,format:t,mimetype:r,content:"",size:0,writable:!0,type:"file"};break}}let u=n.path;return await(await this.storage).setItem(u,n),n}async copy(e,t){let i=ee.PathExt.basename(e);for(t=""===t?"":`${t.slice(1)}/`;await this.get(`${t}${i}`,{content:!0});){let e=ee.PathExt.extname(i);i=`${i.replace(e,"")} (copy)${e}`}let a=`${t}${i}`,n=await this.get(e,{content:!0});if(!n)throw Error(`Could not find file with path ${e}`);return n={...n,name:i,path:a},await(await this.storage).setItem(a,n),n}async get(e,t){if(""===(e=decodeURIComponent(e.replace(/^\//,""))))return await this._getFolder(e);let i=await this.storage,a=await i.getItem(e),n=await this._getServerContents(e,t),o=a||n;if(!o)return null;if(null==t||!t.content)return{size:0,...o,content:null};if("directory"===o.type){let t=new Map;await i.iterate(((i,a)=>{a===`${e}/${i.name}`&&t.set(i.name,i)}));let a=n?n.content:Array.from((await this._getServerDirectory(e)).values());for(let e of a)t.has(e.name)||t.set(e.name,e);let r=[...t.values()];return{name:ee.PathExt.basename(e),path:e,last_modified:o.last_modified,created:o.created,format:"json",mimetype:G.JSON,content:r,size:0,writable:!0,type:"directory"}}return o}async rename(e,t){let i=decodeURIComponent(e),a=await this.get(i,{content:!0});if(!a)throw Error(`Could not find file with path ${i}`);let n=(new Date).toISOString(),o=ee.PathExt.basename(t),r={...a,name:o,path:t,last_modified:n},l=await this.storage;if(await l.setItem(t,r),await l.removeItem(i),await(await this.checkpoints).removeItem(i),"directory"===a.type){let i;for(i of a.content)await this.rename(le.URLExt.join(e,i.name),le.URLExt.join(t,i.name))}return r}async save(e,t={}){var i;e=decodeURIComponent(e);let a=ee.PathExt.extname(null!==(i=t.name)&&void 0!==i?i:""),n=t.chunk,o=!!n&&(n>1||-1===n),r=await this.get(e,{content:o});if(r||(r=await this.newUntitled({path:e,ext:a,type:"file"})),!r)return null;let l=r.content,s=(new Date).toISOString();if(r={...r,...t,last_modified:s},t.content&&"base64"===t.format){let e=!n||-1===n;if(".ipynb"===a){let i=this._handleChunk(t.content,l,o);r={...r,content:e?JSON.parse(i):i,format:"json",type:"notebook",size:i.length}}else if(re.hasFormat(a,"json")){let i=this._handleChunk(t.content,l,o);r={...r,content:e?JSON.parse(i):i,format:"json",type:"file",size:i.length}}else if(re.hasFormat(a,"text")){let e=this._handleChunk(t.content,l,o);r={...r,content:e,format:"text",type:"file",size:e.length}}else{let e=t.content;r={...r,content:e,size:atob(e).length}}}return await(await this.storage).setItem(e,r),r}async delete(e){let t=`${e=decodeURIComponent(e)}/`,i=(await(await this.storage).keys()).filter((i=>i===e||i.startsWith(t)));await Promise.all(i.map(this.forgetPath,this))}async forgetPath(e){await Promise.all([(await this.storage).removeItem(e),(await this.checkpoints).removeItem(e)])}async createCheckpoint(e){var t;let i=await this.checkpoints;e=decodeURIComponent(e);let a=await this.get(e,{content:!0});if(!a)throw Error(`Could not find file with path ${e}`);let n=(null!==(t=await i.getItem(e))&&void 0!==t?t:[]).filter(Boolean);return n.push(a),n.length>Oi&&n.splice(0,n.length-Oi),await i.setItem(e,n),{id:""+(n.length-1),last_modified:a.last_modified}}async listCheckpoints(e){return(await(await this.checkpoints).getItem(e)||[]).filter(Boolean).map(this.normalizeCheckpoint,this)}normalizeCheckpoint(e,t){return{id:t.toString(),last_modified:e.last_modified}}async restoreCheckpoint(e,t){e=decodeURIComponent(e);let i=(await(await this.checkpoints).getItem(e)||[])[parseInt(t)];await(await this.storage).setItem(e,i)}async deleteCheckpoint(e,t){e=decodeURIComponent(e);let i=await(await this.checkpoints).getItem(e)||[],a=parseInt(t);i.splice(a,1),await(await this.checkpoints).setItem(e,i)}_handleChunk(e,t,i){let a=decodeURIComponent(escape(atob(e)));return i?t+a:a}async _getFolder(e){let t=new Map;await(await this.storage).iterate(((e,i)=>{i.includes("/")||t.set(e.path,e)}));for(let i of(await this._getServerDirectory(e)).values())t.has(i.path)||t.set(i.path,i);return e&&0===t.size?null:{name:"",path:e,last_modified:new Date(0).toISOString(),created:new Date(0).toISOString(),format:"json",mimetype:G.JSON,content:Array.from(t.values()),size:0,writable:!0,type:"directory"}}async _getServerContents(e,t){let i=ee.PathExt.basename(e),a=(await this._getServerDirectory(le.URLExt.join(e,".."))).get(i);if(!a)return null;if(a=a||{name:i,path:e,last_modified:new Date(0).toISOString(),created:new Date(0).toISOString(),format:"text",mimetype:G.PLAIN_TEXT,type:"file",writable:!0,size:0,content:""},null!=t&&t.content)if("directory"===a.type){let t=await this._getServerDirectory(e);a={...a,content:Array.from(t.values())}}else{let t=le.URLExt.join(le.PageConfig.getBaseUrl(),"files",e),n=await fetch(t);if(!n.ok)return null;let o=a.mimetype||n.headers.get("Content-Type"),r=ee.PathExt.extname(i);if("notebook"===a.type||re.hasFormat(r,"json")||-1!==(null==o?void 0:o.indexOf("json"))||e.match(/\.(ipynb|[^/]*json[^/]*)$/)){let e=await n.text();a={...a,content:JSON.parse(e),format:"json",mimetype:a.mimetype||G.JSON,size:e.length}}else if(re.hasFormat(r,"text")||-1!==o.indexOf("text")){let e=await n.text();a={...a,content:e,format:"text",mimetype:o||G.PLAIN_TEXT,size:e.length}}else{let e=await n.arrayBuffer(),t=new Uint8Array(e);a={...a,content:btoa(t.reduce(this.reduceBytesToString,"")),format:"base64",mimetype:o||G.OCTET_STREAM,size:t.length}}}return a}async _getServerDirectory(e){let t=this._serverContents.get(e)||new Map;if(!this._serverContents.has(e)){let i=le.URLExt.join(le.PageConfig.getBaseUrl(),"api/contents",e,"all.json");try{let e=await fetch(i),a=JSON.parse(await e.text());for(let e of a.content)t.set(e.name,e)}catch(e){console.warn(`don't worry, about ${e}... nothing's broken. If there had been a\n          file at ${i}, you might see some more files.`)}this._serverContents.set(e,t)}return t}async _incrementCounter(e){var t;let i=await this.counters,a=(null!==(t=await i.getItem(e))&&void 0!==t?t:-1)+1;return await i.setItem(e,a),a}},(qe||(qe={})).EMPTY_NB={metadata:{orig_nbformat:4},nbformat_minor:4,nbformat:4,cells:[]}}));function pt(e){return"node"in e}var _e,ji,Si,Ei,dt=pe((()=>{_e=16895,ji=33206,Si=1,Ei=2})),zi,mt,$e,kn,On,Ri,Le,Fe,he,Be,ke,We=pe((()=>{dt(),zi=":",mt="/api/drive.v1",$e=4096,kn=new TextEncoder,On=new TextDecoder("utf-8"),Ri={0:!1,1:!0,2:!0,64:!0,65:!0,66:!0,129:!0,193:!0,514:!0,577:!0,578:!0,705:!0,706:!0,1024:!0,1025:!0,1026:!0,1089:!0,1090:!0,1153:!0,1154:!0,1217:!0,1218:!0,4096:!0,4098:!0},Le=class{constructor(e){this.fs=e}open(e){let t=this.fs.realPath(e.node);this.fs.FS.isFile(e.node.mode)&&(e.file=this.fs.API.get(t))}close(e){if(!this.fs.FS.isFile(e.node.mode)||!e.file)return;let t=this.fs.realPath(e.node),i=e.flags,a="string"==typeof i?parseInt(i,10):i;a&=8191;let n=!0;a in Ri&&(n=Ri[a]),n&&this.fs.API.put(t,e.file),e.file=void 0}read(e,t,i,a,n){if(a<=0||void 0===e.file||n>=(e.file.data.length||0))return 0;let o=Math.min(e.file.data.length-n,a);return t.set(e.file.data.subarray(n,n+o),i),o}write(e,t,i,a,n){var o;if(a<=0||void 0===e.file)return 0;if(e.node.timestamp=Date.now(),n+a>((null===(o=e.file)||void 0===o?void 0:o.data.length)||0)){let t=e.file.data?e.file.data:new Uint8Array;e.file.data=new Uint8Array(n+a),e.file.data.set(t)}return e.file.data.set(t.subarray(i,i+a),n),a}llseek(e,t,i){let a=t;if(1===i)a+=e.position;else if(2===i&&this.fs.FS.isFile(e.node.mode)){if(void 0===e.file)throw new this.fs.FS.ErrnoError(this.fs.ERRNO_CODES.EPERM);a+=e.file.data.length}if(a<0)throw new this.fs.FS.ErrnoError(this.fs.ERRNO_CODES.EINVAL);return a}},Fe=class{constructor(e){this.fs=e}node(e){return pt(e)?e.node:e}getattr(e){let t=this.node(e);return{...this.fs.API.getattr(this.fs.realPath(t)),mode:t.mode,ino:t.id}}setattr(e,t){let i=this.node(e);for(let[e,a]of Object.entries(t))switch(e){case"mode":i.mode=a;break;case"timestamp":i.timestamp=a;break;default:console.warn("setattr",e,"of",a,"on",i,"not yet implemented")}}lookup(e,t){let i=this.node(e),a=this.fs.PATH.join2(this.fs.realPath(i),t),n=this.fs.API.lookup(a);if(!n.ok)throw this.fs.FS.genericErrors[this.fs.ERRNO_CODES.ENOENT];return this.fs.createNode(i,t,n.mode,0)}mknod(e,t,i,a){let n=this.node(e),o=this.fs.PATH.join2(this.fs.realPath(n),t);return this.fs.API.mknod(o,i),this.fs.createNode(n,t,i,a)}rename(e,t,i){let a=this.node(e),n=this.node(t);this.fs.API.rename(a.parent?this.fs.PATH.join2(this.fs.realPath(a.parent),a.name):a.name,this.fs.PATH.join2(this.fs.realPath(n),i)),a.name=i,a.parent=n}unlink(e,t){this.fs.API.rmdir(this.fs.PATH.join2(this.fs.realPath(this.node(e)),t))}rmdir(e,t){this.fs.API.rmdir(this.fs.PATH.join2(this.fs.realPath(this.node(e)),t))}readdir(e){return this.fs.API.readdir(this.fs.realPath(this.node(e)))}symlink(e,t,i){throw new this.fs.FS.ErrnoError(this.fs.ERRNO_CODES.EPERM)}readlink(e){throw new this.fs.FS.ErrnoError(this.fs.ERRNO_CODES.EPERM)}},he=class{constructor(e,t,i,a){this._driveName=e,this._mountpoint=t,this.FS=i,this.ERRNO_CODES=a}lookup(e){return this.request({method:"lookup",path:this.normalizePath(e)})}getmode(e){return this.request({method:"getmode",path:this.normalizePath(e)})}mknod(e,t){return this.request({method:"mknod",path:this.normalizePath(e),data:{mode:t}})}rename(e,t){return this.request({method:"rename",path:this.normalizePath(e),data:{newPath:this.normalizePath(t)}})}readdir(e){let t=this.request({method:"readdir",path:this.normalizePath(e)});return t.push("."),t.push(".."),t}rmdir(e){return this.request({method:"rmdir",path:this.normalizePath(e)})}get(e){let t=this.request({method:"get",path:this.normalizePath(e)});if(!t)throw new this.FS.ErrnoError(this.ERRNO_CODES.ENOENT);let i=t.content,a=t.format;switch(a){case"json":case"text":return{data:kn.encode(i),format:a};case"base64":{let e=atob(i),t=e.length,n=new Uint8Array(t);for(let i=0;i<t;i++)n[i]=e.charCodeAt(i);return{data:n,format:a}}default:throw new this.FS.ErrnoError(this.ERRNO_CODES.ENOENT)}}put(e,t){switch(t.format){case"json":case"text":return this.request({method:"put",path:this.normalizePath(e),data:{format:t.format,data:On.decode(t.data)}});case"base64":{let i="";for(let e=0;e<t.data.byteLength;e++)i+=String.fromCharCode(t.data[e]);return this.request({method:"put",path:this.normalizePath(e),data:{format:t.format,data:btoa(i)}})}}}getattr(e){let t=this.request({method:"getattr",path:this.normalizePath(e)});return t.atime&&(t.atime=new Date(t.atime)),t.mtime&&(t.mtime=new Date(t.mtime)),t.ctime&&(t.ctime=new Date(t.ctime)),t.size=t.size||0,t}normalizePath(e){return e.startsWith(this._mountpoint)&&(e=e.slice(this._mountpoint.length)),this._driveName&&(e=`${this._driveName}${zi}${e}`),e}},Be=class extends he{constructor(e,t,i,a,n){super(t,i,a,n),this._baseUrl=e}request(e){let t=new XMLHttpRequest;t.open("POST",encodeURI(this.endpoint),!1);try{t.send(JSON.stringify(e))}catch(e){console.error(e)}if(t.status>=400)throw new this.FS.ErrnoError(this.ERRNO_CODES.EINVAL);return JSON.parse(t.responseText)}get endpoint(){return`${this._baseUrl}api/drive`}},ke=class{constructor(e){this.FS=e.FS,this.PATH=e.PATH,this.ERRNO_CODES=e.ERRNO_CODES,this.API=this.createAPI(e),this.driveName=e.driveName,this.node_ops=new Fe(this),this.stream_ops=new Le(this)}createAPI(e){return new Be(e.baseUrl,e.driveName,e.mountpoint,e.FS,e.ERRNO_CODES)}mount(e){return this.createNode(null,e.mountpoint,16895,0)}createNode(e,t,i,a){let n=this.FS;if(!n.isDir(i)&&!n.isFile(i))throw new n.ErrnoError(this.ERRNO_CODES.EINVAL);let o=n.createNode(e,t,i,a);return o.node_ops=this.node_ops,o.stream_ops=this.stream_ops,o}getMode(e){return this.API.getmode(e)}realPath(e){let t=[],i=e;for(t.push(i.name);i.parent!==i;)i=i.parent,t.push(i.name);return t.reverse(),this.PATH.join.apply(null,t)}}})),ft,Oe,ut=pe((()=>{ft=de(be()),We(),Oe=class{constructor(e){this.contentsManager=e.contentsManager}async processDriveRequest(e){switch(e.method){case"readdir":return this.readdir(e);case"rmdir":return this.rmdir(e);case"rename":return this.rename(e);case"getmode":return this.getmode(e);case"lookup":return this.lookup(e);case"mknod":return this.mknod(e);case"getattr":return this.getattr(e);case"get":return this.get(e);case"put":return this.put(e)}throw`Drive request ${e.method} does not exist.`}async readdir(e){let t=await this.contentsManager.get(e.path,{content:!0}),i=[];return"directory"===t.type&&t.content&&(i=t.content.map((e=>e.name))),i}async rmdir(e){return await this.contentsManager.delete(e.path),null}async rename(e){return await this.contentsManager.rename(e.path,e.data.newPath),null}async getmode(e){let t;return t="directory"===(await this.contentsManager.get(e.path)).type?16895:33206,t}async lookup(e){let t;try{t={ok:!0,mode:"directory"===(await this.contentsManager.get(e.path)).type?16895:33206}}catch{t={ok:!1}}return t}async mknod(e){let t=await this.contentsManager.newUntitled({path:ft.PathExt.dirname(e.path),type:16895===e.data.mode?"directory":"file",ext:ft.PathExt.extname(e.path)});return await this.contentsManager.rename(t.path,e.path),null}async getattr(e){let t=await this.contentsManager.get(e.path),i=new Date(0).toISOString();return{dev:1,nlink:1,uid:0,gid:0,rdev:0,size:t.size||0,blksize:$e,blocks:Math.ceil(t.size||0/$e),atime:t.last_modified||i,mtime:t.last_modified||i,ctime:t.created||i,timestamp:0}}async get(e){let t,i=await this.contentsManager.get(e.path,{content:!0});return"directory"!==i.type&&(t={content:"json"===i.format?JSON.stringify(i.content):i.content,format:i.format}),t}async put(e){return await this.contentsManager.save(e.path,{content:"json"===e.data.format?JSON.parse(e.data.data):e.data.data,type:"file",format:e.data.format}),null}}})),ht,Ii=pe((()=>{We(),ut(),ht=class{constructor(e){this.isDisposed=!1,this._onMessage=async e=>{if(!this._channel)return;let t=e.data;if("broadcast.ts"!==(null==t?void 0:t.receiver))return;let i=await this._driveContentsProcessor.processDriveRequest(t);this._channel.postMessage(i)},this._channel=null,this._enabled=!1,this._contents=e.contents,this._driveContentsProcessor=new Oe({contentsManager:this._contents})}get enabled(){return this._enabled}enable(){this._channel?console.warn("BroadcastChannel already created and enabled"):(this._channel=new BroadcastChannel(mt),this._channel.addEventListener("message",this._onMessage),this._enabled=!0)}disable(){this._channel&&(this._channel.removeEventListener("message",this._onMessage),this._channel=null),this._enabled=!1}dispose(){this.isDisposed||(this.disable(),this.isDisposed=!0)}}})),Ti={};Ki(Ti,{BLOCK_SIZE:()=>$e,BroadcastChannelWrapper:()=>ht,Contents:()=>ct,ContentsAPI:()=>he,DIR_MODE:()=>_e,DRIVE_API_PATH:()=>mt,DRIVE_SEPARATOR:()=>zi,DriveContentsProcessor:()=>Oe,DriveFS:()=>ke,DriveFSEmscriptenNodeOps:()=>Fe,DriveFSEmscriptenStreamOps:()=>Le,FILE:()=>re,FILE_MODE:()=>ji,IBroadcastChannelWrapper:()=>_n,IContents:()=>bn,MIME:()=>G,SEEK_CUR:()=>Si,SEEK_END:()=>Ei,ServiceWorkerContentsAPI:()=>Be,instanceOfStream:()=>pt});var vt=pe((()=>{Pi(),We(),lt(),Ii(),dt(),ut()})),Je="function",ie="64e10b34-2bf7-4616-9668-f99de5aa046e",Sn="M"+ie,En="T"+ie,yt="get",bt="has",_t="set",{isArray:xe}=Array,{SharedArrayBuffer:we,window:Vi}=globalThis,{notify:Ve,wait:Ye,waitAsync:ye}=Atomics,Ge=null;ye||(ye=e=>({value:new Promise((t=>{let i=new Worker("data:application/javascript,onmessage%3D(%7Bdata%3Ab%7D)%3D%3E(Atomics.wait(b%2C0)%2CpostMessage(0))");i.onmessage=t,i.postMessage(e)}))}));try{new we(4)}catch{we=ArrayBuffer;let e=new WeakMap;if(Vi){let t=new Map,{prototype:{postMessage:i}}=Worker,a=e=>{var i;let a=null==(i=e.data)?void 0:i[ie];if(!xe(a)){e.stopImmediatePropagation();let{id:i,sb:n}=a;t.get(i)(n)}};Ge=function(t,...n){let o=null==t?void 0:t[ie];if(xe(o)){let[t,i]=o;e.set(i,t),this.addEventListener("message",a)}return i.call(this,t,...n)},ye=i=>({value:new Promise((a=>{t.set(e.get(i),a)})).then((a=>{t.delete(e.get(i)),e.delete(i);for(let e=0;e<a.length;e++)i[e]=a[e];return"ok"}))})}else{let t=(e,t)=>({[ie]:{id:e,sb:t}});Ve=i=>{postMessage(t(e.get(i),i))},addEventListener("message",(t=>{var i;let a=null==(i=t.data)?void 0:i[ie];if(xe(a)){let[t,i]=a;e.set(i,t)}}))}}var{Int32Array:Xe,Map:kt,Uint16Array:Qe}=globalThis,{BYTES_PER_ELEMENT:Ot}=Xe,{BYTES_PER_ELEMENT:Yi}=Qe,Gi=(e,t,i)=>{for(;"timed-out"===Ye(e,0,0,t);)i()},et=new WeakSet,Ze=new WeakMap,Zi={value:{then:e=>e()}},Xi=0,Ct=(e,{parse:t=JSON.parse,stringify:i=JSON.stringify,transform:a,interrupt:n}=JSON)=>{if(!Ze.has(e)){let o=Ge||e.postMessage,r=(t,...i)=>o.call(e,{[ie]:i},{transfer:t}),l=typeof n===Je?n:null==n?void 0:n.handler,s=(null==n?void 0:n.delay)||42,p=new TextDecoder("utf-16"),c=(e,t)=>e?ye(t,0):(l?Gi(t,s,l):Ye(t,0),Zi),d=!1;Ze.set(e,new Proxy(new kt,{[bt]:(e,t)=>"string"==typeof t&&!t.startsWith("_"),[yt]:(i,n)=>"then"===n?null:(...i)=>{let o=Xi++,l=new Xe(new we(2*Ot)),s=[];et.has(i.at(-1)||s)&&et.delete(s=i.pop()),r(s,o,l,n,a?i.map(a):i);let m=e!==globalThis,u=0;return d&&m&&(u=setTimeout(console.warn,1e3,`💀🔒 - Possible deadlock if proxy.${n}(...args) is awaited`)),c(m,l).value.then((()=>{clearTimeout(u);let e=l[1];if(!e)return;let i=Yi*e;return l=new Xe(new we(i+i%Ot)),r([],o,l),c(m,l).value.then((()=>t(p.decode(new Qe(l.buffer).slice(0,e)))))}))},[_t](t,n,o){let r=typeof o;if(r!==Je)throw new Error(`Unable to assign ${n} as ${r}`);if(!t.size){let n=new kt;e.addEventListener("message",(async e=>{var o;let r=null==(o=e.data)?void 0:o[ie];if(xe(r)){e.stopImmediatePropagation();let o,[l,s,...p]=r;if(p.length){let[e,r]=p;if(t.has(e)){d=!0;try{let o=await t.get(e)(...r);if(void 0!==o){let e=i(a?a(o):o);n.set(l,e),s[1]=e.length}}catch(e){o=e}finally{d=!1}}else o=new Error(`Unsupported action: ${e}`);s[0]=1}else{let e=n.get(l);n.delete(l);for(let t=new Qe(s.buffer),i=0;i<e.length;i++)t[i]=e.charCodeAt(i)}if(Ve(s,0),o)throw o}}))}return!!t.set(n,o)}}))}return Ze.get(e)};Ct.transfer=(...e)=>(et.add(e),e);var Pt=Ct;vt();var He=class{constructor(){this._options=null,this._initializer=null,this._pyodide=null,this._localPath="",this._driveName="",this._driveFS=null,this._sendWorkerMessage=()=>{},this._initialized=new Promise(((e,t)=>{this._initializer={resolve:e,reject:t}}))}async initialize(e){var t;if(this._options=e,e.location.includes(":")){let t=e.location.split(":");this._driveName=t[0],this._localPath=t[1]}else this._driveName="",this._localPath=e.location;await this.initRuntime(e),await this.initFilesystem(e),await this.initPackageManager(e),await this.initKernel(e),await this.initGlobals(e),null==(t=this._initializer)||t.resolve()}async initRuntime(e){let t,{pyodideUrl:i,indexUrl:a}=e;i.endsWith(".mjs")?t=(await __webpack_require__(903)(i)).loadPyodide:(importScripts(i),t=self.loadPyodide),this._pyodide=await t({indexURL:a,...e.loadPyodideOptions})}async initPackageManager(e){if(!this._options)throw new Error("Uninitialized");let{pipliteWheelUrl:t,disablePyPIFallback:i,pipliteUrls:a,loadPyodideOptions:n}=this._options,o=(n||{}).packages||[];o.includes("micropip")||await this._pyodide.loadPackage(["micropip"]),o.includes("piplite")||await this._pyodide.runPythonAsync(`\n      import micropip\n      await micropip.install('${t}', keep_going=True)\n    `),await this._pyodide.runPythonAsync(`\n      import piplite.piplite\n      piplite.piplite._PIPLITE_DISABLE_PYPI = ${i?"True":"False"}\n      piplite.piplite._PIPLITE_URLS = ${JSON.stringify(a)}\n    `)}async initKernel(e){let t=(e.loadPyodideOptions||{}).packages||[],i=["ssl","sqlite3","ipykernel","comm","pyodide_kernel","ipython"],a=[];for(let e of i)t.includes(e)||a.push(`await piplite.install('${e}', keep_going=True)`);a.push("import pyodide_kernel"),e.mountDrive&&this._localPath&&a.push("import os",`os.chdir("${this._localPath}")`),await this._pyodide.runPythonAsync(a.join("\n"))}async initGlobals(e){let{globals:t}=this._pyodide;this._kernel=t.get("pyodide_kernel").kernel_instance.copy(),this._stdout_stream=t.get("pyodide_kernel").stdout_stream.copy(),this._stderr_stream=t.get("pyodide_kernel").stderr_stream.copy(),this._interpreter=this._kernel.interpreter.copy(),this._interpreter.send_comm=this.sendComm.bind(this)}async initFilesystem(e){if(e.mountDrive){let t="/drive",{FS:i,PATH:a,ERRNO_CODES:n}=this._pyodide,{baseUrl:o}=e,{DriveFS:r}=await Promise.resolve().then((()=>(vt(),Ti))),l=new r({FS:i,PATH:a,ERRNO_CODES:n,baseUrl:o,driveName:this._driveName,mountpoint:t});i.mkdir(t),i.mount(l,{},t),i.chdir(t),this._driveFS=l}}mapToObject(e){let t=e instanceof Array?[]:{};return e.forEach(((e,i)=>{t[i]=e instanceof Map||e instanceof Array?this.mapToObject(e):e})),t}formatResult(e){if(!(e instanceof this._pyodide.ffi.PyProxy))return e;let t=e.toJs();return this.mapToObject(t)}registerCallback(e){this._sendWorkerMessage=e}async setup(e){await this._initialized,this._kernel._parent_header=this._pyodide.toPy(e)}async execute(e,t){await this.setup(t);let i=(e,t)=>{let i={name:this.formatResult(e),text:this.formatResult(t)};this._sendWorkerMessage({parentHeader:this.formatResult(this._kernel._parent_header).header,bundle:i,type:"stream"})};this._stdout_stream.publish_stream_callback=i,this._stderr_stream.publish_stream_callback=i,this._interpreter.display_pub.clear_output_callback=e=>{let t={wait:this.formatResult(e)};this._sendWorkerMessage({parentHeader:this.formatResult(this._kernel._parent_header).header,bundle:t,type:"clear_output"})},this._interpreter.display_pub.display_data_callback=(e,t,i)=>{let a={data:this.formatResult(e),metadata:this.formatResult(t),transient:this.formatResult(i)};this._sendWorkerMessage({parentHeader:this.formatResult(this._kernel._parent_header).header,bundle:a,type:"display_data"})},this._interpreter.display_pub.update_display_data_callback=(e,t,i)=>{let a={data:this.formatResult(e),metadata:this.formatResult(t),transient:this.formatResult(i)};this._sendWorkerMessage({parentHeader:this.formatResult(this._kernel._parent_header).header,bundle:a,type:"update_display_data"})},this._interpreter.displayhook.publish_execution_result=(e,t,i)=>{let a={execution_count:e,data:this.formatResult(t),metadata:this.formatResult(i)};this._sendWorkerMessage({parentHeader:this.formatResult(this._kernel._parent_header).header,bundle:a,type:"execute_result"})},this._interpreter.input=this.input.bind(this),this._interpreter.getpass=this.getpass.bind(this);let a=await this._kernel.run(e.code),n=this.formatResult(a);return"error"===n.status&&((e,t,i)=>{let a={ename:e,evalue:t,traceback:i};this._sendWorkerMessage({parentHeader:this.formatResult(this._kernel._parent_header).header,bundle:a,type:"execute_error"})})(n.ename,n.evalue,n.traceback),n}async complete(e,t){await this.setup(t);let i=this._kernel.complete(e.code,e.cursor_pos);return this.formatResult(i)}async inspect(e,t){await this.setup(t);let i=this._kernel.inspect(e.code,e.cursor_pos,e.detail_level);return this.formatResult(i)}async isComplete(e,t){await this.setup(t);let i=this._kernel.is_complete(e.code);return this.formatResult(i)}async commInfo(e,t){await this.setup(t);let i=this._kernel.comm_info(e.target_name);return{comms:this.formatResult(i),status:"ok"}}async commOpen(e,t){await this.setup(t);let i=this._kernel.comm_manager.comm_open(this._pyodide.toPy(null),this._pyodide.toPy(null),this._pyodide.toPy(e));return this.formatResult(i)}async commMsg(e,t){await this.setup(t);let i=this._kernel.comm_manager.comm_msg(this._pyodide.toPy(null),this._pyodide.toPy(null),this._pyodide.toPy(e));return this.formatResult(i)}async commClose(e,t){await this.setup(t);let i=this._kernel.comm_manager.comm_close(this._pyodide.toPy(null),this._pyodide.toPy(null),this._pyodide.toPy(e));return this.formatResult(i)}async inputReply(e,t){await this.setup(t),this._resolveInputReply(e)}async sendInputRequest(e,t){let i={prompt:e,password:t};this._sendWorkerMessage({type:"input_request",parentHeader:this.formatResult(this._kernel._parent_header).header,content:i})}async getpass(e){return e=void 0===e?"":e,await this.sendInputRequest(e,!0),(await new Promise((e=>{this._resolveInputReply=e}))).value}async input(e){return e=void 0===e?"":e,await this.sendInputRequest(e,!1),(await new Promise((e=>{this._resolveInputReply=e}))).value}async sendComm(e,t,i,a,n){this._sendWorkerMessage({type:e,content:this.formatResult(t),metadata:this.formatResult(i),ident:this.formatResult(a),buffers:this.formatResult(n),parentHeader:this.formatResult(this._kernel._parent_header).header})}},Z=Pt(self),gt=class extends he{request(e){return Z.processDriveRequest(e)}},xt=class extends ke{createAPI(e){return new gt(e.driveName,e.mountpoint,e.FS,e.ERRNO_CODES)}},wt=class extends He{async initFilesystem(e){if(e.mountDrive){let t="/drive",{FS:i,PATH:a,ERRNO_CODES:n}=this._pyodide,{baseUrl:o}=e,r=new xt({FS:i,PATH:a,ERRNO_CODES:n,baseUrl:o,driveName:this._driveName,mountpoint:t});i.mkdir(t),i.mount(r,{},t),i.chdir(t),this._driveFS=r}}},B=new wt,Cn=Z.processWorkerMessage.bind(Z);B.registerCallback(Cn),Z.initialize=B.initialize.bind(B),Z.execute=B.execute.bind(B),Z.complete=B.complete.bind(B),Z.inspect=B.inspect.bind(B),Z.isComplete=B.isComplete.bind(B),Z.commInfo=B.commInfo.bind(B),Z.commOpen=B.commOpen.bind(B),Z.commMsg=B.commMsg.bind(B),Z.commClose=B.commClose.bind(B),Z.inputReply=B.inputReply.bind(B)}}]);
//# sourceMappingURL=746.thebe-lite.min.js.map