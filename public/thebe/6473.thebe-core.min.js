"use strict";(self.webpackChunkthebe_core=self.webpackChunkthebe_core||[]).push([[6473],{96473:(e,t,n)=>{n.r(t),n.d(t,{protobuf:()=>u});var r=["package","message","import","syntax","required","optional","repeated","reserved","default","extensions","packed","bool","bytes","double","enum","float","string","int32","int64","uint32","uint64","sint32","sint64","fixed32","fixed64","sfixed32","sfixed64","option","service","rpc","returns"],a=new RegExp("^(("+r.join(")|(")+"))\\b","i"),i=new RegExp("^[_A-Za-z¡-￿][_A-Za-z0-9¡-￿]*");const u={name:"protobuf",token:function(e){if(e.eatSpace())return null;if(e.match("//"))return e.skipToEnd(),"comment";if(e.match(/^[0-9\.+-]/,!1)){if(e.match(/^[+-]?0x[0-9a-fA-F]+/))return"number";if(e.match(/^[+-]?\d*\.\d+([EeDd][+-]?\d+)?/))return"number";if(e.match(/^[+-]?\d+([EeDd][+-]?\d+)?/))return"number"}return e.match(/^"([^"]|(""))*"/)||e.match(/^'([^']|(''))*'/)?"string":e.match(a)?"keyword":e.match(i)?"variable":(e.next(),null)},languageData:{autocomplete:r}}}}]);
//# sourceMappingURL=6473.thebe-core.min.js.map