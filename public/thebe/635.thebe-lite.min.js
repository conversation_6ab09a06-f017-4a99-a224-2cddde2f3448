/*! For license information please see 635.thebe-lite.min.js.LICENSE.txt */
"use strict";(self.webpackChunkthebe_lite=self.webpackChunkthebe_lite||[]).push([[635],{7992:(e,t,s)=>{s.d(t,{PyodideKernel:()=>te});const n="function",a="64e10b34-2bf7-4616-9668-f99de5aa046e",r="get",i="has",o="set",{isArray:c}=Array;let{SharedArrayBuffer:l,window:p}=globalThis,{notify:d,wait:u,waitAsync:h}=Atomics,g=null;h||(h=e=>({value:new Promise((t=>{let s=new Worker("data:application/javascript,onmessage%3D(%7Bdata%3Ab%7D)%3D%3E(Atomics.wait(b%2C0)%2CpostMessage(0))");s.onmessage=t,s.postMessage(e)}))}));try{new l(4)}catch(e){l=ArrayBuffer;const t=new WeakMap;if(p){const e=new Map,{prototype:{postMessage:s}}=Worker,n=t=>{const s=t.data?.[a];if(!c(s)){t.stopImmediatePropagation();const{id:n,sb:a}=s;e.get(n)(a)}};g=function(e,...r){const i=e?.[a];if(c(i)){const[e,s]=i;t.set(s,e),this.addEventListener("message",n)}return s.call(this,e,...r)},h=s=>({value:new Promise((n=>{e.set(t.get(s),n)})).then((n=>{e.delete(t.get(s)),t.delete(s);for(let e=0;e<n.length;e++)s[e]=n[e];return"ok"}))})}else{const e=(e,t)=>({[a]:{id:e,sb:t}});d=s=>{postMessage(e(t.get(s),s))},addEventListener("message",(e=>{const s=e.data?.[a];if(c(s)){const[e,n]=s;t.set(n,e)}}))}}const{Int32Array:m,Map:y,Uint16Array:_}=globalThis,{BYTES_PER_ELEMENT:f}=m,{BYTES_PER_ELEMENT:v}=_,b=new WeakSet,w=new WeakMap,M={value:{then:e=>e()}};let k=0;const E=(e,{parse:t=JSON.parse,stringify:s=JSON.stringify,transform:p,interrupt:E}=JSON)=>{if(!w.has(e)){const x=g||e.postMessage,R=(t,...s)=>x.call(e,{[a]:s},{transfer:t}),H=typeof E===n?E:E?.handler,T=E?.delay||42,P=new TextDecoder("utf-16"),C=(e,t)=>e?h(t,0):(H?((e,t,s)=>{for(;"timed-out"===u(e,0,0,t);)s()})(t,T,H):u(t,0),M);let K=!1;w.set(e,new Proxy(new y,{[i]:(e,t)=>"string"==typeof t&&!t.startsWith("_"),[r]:(s,n)=>"then"===n?null:(...s)=>{const a=k++;let r=new m(new l(2*f)),i=[];b.has(s.at(-1)||i)&&b.delete(i=s.pop()),R(i,a,r,n,p?s.map(p):s);const o=e!==globalThis;let c=0;return K&&o&&(c=setTimeout(console.warn,1e3,`💀🔒 - Possible deadlock if proxy.${n}(...args) is awaited`)),C(o,r).value.then((()=>{clearTimeout(c);const e=r[1];if(!e)return;const s=v*e;return r=new m(new l(s+s%f)),R([],a,r),C(o,r).value.then((()=>t(P.decode(new _(r.buffer).slice(0,e)))))}))},[o](t,r,i){const o=typeof i;if(o!==n)throw new Error(`Unable to assign ${r} as ${o}`);if(!t.size){const n=new y;e.addEventListener("message",(async e=>{const r=e.data?.[a];if(c(r)){e.stopImmediatePropagation();const[a,i,...o]=r;let c;if(o.length){const[e,r]=o;if(t.has(e)){K=!0;try{const o=await t.get(e)(...r);if(void 0!==o){const e=s(p?p(o):o);n.set(a,e),i[1]=e.length}}catch(e){c=e}finally{K=!1}}else c=new Error(`Unsupported action: ${e}`);i[0]=1}else{const e=n.get(a);n.delete(a);for(let t=new _(i.buffer),s=0;s<e.length;s++)t[s]=e.charCodeAt(s)}if(d(i,0),c)throw c}}))}return!!t.set(r,i)}}))}return w.get(e)};E.transfer=(...e)=>(b.add(e),e);const x=E,R=Symbol("Comlink.proxy"),H=Symbol("Comlink.endpoint"),T=Symbol("Comlink.releaseProxy"),P=Symbol("Comlink.finalizer"),C=Symbol("Comlink.thrown"),K=e=>"object"==typeof e&&null!==e||"function"==typeof e,A=new Map([["proxy",{canHandle:e=>K(e)&&e[R],serialize(e){const{port1:t,port2:s}=new MessageChannel;return q(e,t),[s,[s]]},deserialize:e=>(e.start(),S(e))}],["throw",{canHandle:e=>K(e)&&C in e,serialize({value:e}){let t;return t=e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[t,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error(e.value.message),e.value);throw e.value}}]]);function q(e,t=globalThis,s=["*"]){t.addEventListener("message",(function n(a){if(!a||!a.data)return;if(!function(e,t){for(const s of e){if(t===s||"*"===s)return!0;if(s instanceof RegExp&&s.test(t))return!0}return!1}(s,a.origin))return void console.warn(`Invalid origin '${a.origin}' for comlink proxy`);const{id:r,type:i,path:o}=Object.assign({path:[]},a.data),c=(a.data.argumentList||[]).map(F);let l;try{const t=o.slice(0,-1).reduce(((e,t)=>e[t]),e),s=o.reduce(((e,t)=>e[t]),e);switch(i){case"GET":l=s;break;case"SET":t[o.slice(-1)[0]]=F(a.data.value),l=!0;break;case"APPLY":l=s.apply(t,c);break;case"CONSTRUCT":l=z(new s(...c));break;case"ENDPOINT":{const{port1:t,port2:s}=new MessageChannel;q(e,s),l=function(e,t){return j.set(e,t),e}(t,[t])}break;case"RELEASE":l=void 0;break;default:return}}catch(e){l={value:e,[C]:0}}Promise.resolve(l).catch((e=>({value:e,[C]:0}))).then((s=>{const[a,o]=B(s);t.postMessage(Object.assign(Object.assign({},a),{id:r}),o),"RELEASE"===i&&(t.removeEventListener("message",n),O(t),P in e&&"function"==typeof e[P]&&e[P]())})).catch((e=>{const[s,n]=B({value:new TypeError("Unserializable return value"),[C]:0});t.postMessage(Object.assign(Object.assign({},s),{id:r}),n)}))})),t.start&&t.start()}function O(e){(function(e){return"MessagePort"===e.constructor.name})(e)&&e.close()}function S(e,t){return U(e,[],t)}function D(e){if(e)throw new Error("Proxy has been released and is not useable")}function W(e){return $(e,{type:"RELEASE"}).then((()=>{O(e)}))}const L=new WeakMap,I="FinalizationRegistry"in globalThis&&new FinalizationRegistry((e=>{const t=(L.get(e)||0)-1;L.set(e,t),0===t&&W(e)}));function U(e,t=[],s=function(){}){let n=!1;const a=new Proxy(s,{get(s,r){if(D(n),r===T)return()=>{!function(e){I&&I.unregister(e)}(a),W(e),n=!0};if("then"===r){if(0===t.length)return{then:()=>a};const s=$(e,{type:"GET",path:t.map((e=>e.toString()))}).then(F);return s.then.bind(s)}return U(e,[...t,r])},set(s,a,r){D(n);const[i,o]=B(r);return $(e,{type:"SET",path:[...t,a].map((e=>e.toString())),value:i},o).then(F)},apply(s,a,r){D(n);const i=t[t.length-1];if(i===H)return $(e,{type:"ENDPOINT"}).then(F);if("bind"===i)return U(e,t.slice(0,-1));const[o,c]=N(r);return $(e,{type:"APPLY",path:t.map((e=>e.toString())),argumentList:o},c).then(F)},construct(s,a){D(n);const[r,i]=N(a);return $(e,{type:"CONSTRUCT",path:t.map((e=>e.toString())),argumentList:r},i).then(F)}});return function(e,t){const s=(L.get(t)||0)+1;L.set(t,s),I&&I.register(e,t,e)}(a,e),a}function N(e){const t=e.map(B);return[t.map((e=>e[0])),(s=t.map((e=>e[1])),Array.prototype.concat.apply([],s))];var s}const j=new WeakMap;function z(e){return Object.assign(e,{[R]:!0})}function B(e){for(const[t,s]of A)if(s.canHandle(e)){const[n,a]=s.serialize(e);return[{type:"HANDLER",name:t,value:n},a]}return[{type:"RAW",value:e},j.get(e)||[]]}function F(e){switch(e.type){case"HANDLER":return A.get(e.name).deserialize(e.value);case"RAW":return e.value}}function $(e,t,s){return new Promise((n=>{const a=new Array(4).fill(0).map((()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16))).join("-");e.addEventListener("message",(function t(s){s.data&&s.data.id&&s.data.id===a&&(e.removeEventListener("message",t),n(s.data))})),e.start&&e.start(),e.postMessage(Object.assign({id:a},t),s)}))}var Y=s(5764),G=s(8925),J=s(7883),X=s(8571);class Q{constructor(e){this._history=[],this._executionCount=0,this._isDisposed=!1,this._disposed=new X.Signal(this),this._parentHeader=void 0,this._parent=void 0;const{id:t,name:s,location:n,sendMessage:a}=e;this._id=t,this._name=s,this._location=n,this._sendMessage=a}get ready(){return Promise.resolve()}get isDisposed(){return this._isDisposed}get disposed(){return this._disposed}get id(){return this._id}get name(){return this._name}get location(){return this._location}get executionCount(){return this._executionCount}get parentHeader(){return this._parentHeader}get parent(){return this._parent}dispose(){this.isDisposed||(this._isDisposed=!0,this._disposed.emit(void 0))}async handleMessage(e){switch(this._busy(e),this._parent=e,e.header.msg_type){case"kernel_info_request":await this._kernelInfo(e);break;case"execute_request":await this._execute(e);break;case"input_reply":this.inputReply(e.content);break;case"inspect_request":await this._inspect(e);break;case"is_complete_request":await this._isCompleteRequest(e);break;case"complete_request":await this._complete(e);break;case"history_request":await this._historyRequest(e);break;case"comm_open":await this.commOpen(e);break;case"comm_msg":await this.commMsg(e);break;case"comm_close":await this.commClose(e)}this._idle(e)}stream(e,t=void 0){var s;const n=void 0!==t?t:this._parentHeader,a=J.KernelMessage.createMessage({channel:"iopub",msgType:"stream",session:null!==(s=null==n?void 0:n.session)&&void 0!==s?s:"",parentHeader:n,content:e});this._sendMessage(a)}displayData(e,t=void 0){var s,n;const a=void 0!==t?t:this._parentHeader;e.metadata=null!==(s=e.metadata)&&void 0!==s?s:{};const r=J.KernelMessage.createMessage({channel:"iopub",msgType:"display_data",session:null!==(n=null==a?void 0:a.session)&&void 0!==n?n:"",parentHeader:a,content:e});this._sendMessage(r)}inputRequest(e,t=void 0){var s;const n=void 0!==t?t:this._parentHeader,a=J.KernelMessage.createMessage({channel:"stdin",msgType:"input_request",session:null!==(s=null==n?void 0:n.session)&&void 0!==s?s:"",parentHeader:n,content:e});this._sendMessage(a)}publishExecuteResult(e,t=void 0){var s;const n=void 0!==t?t:this._parentHeader,a=J.KernelMessage.createMessage({channel:"iopub",msgType:"execute_result",session:null!==(s=null==n?void 0:n.session)&&void 0!==s?s:"",parentHeader:n,content:e});this._sendMessage(a)}publishExecuteError(e,t=void 0){var s;const n=void 0!==t?t:this._parentHeader,a=J.KernelMessage.createMessage({channel:"iopub",msgType:"error",session:null!==(s=null==n?void 0:n.session)&&void 0!==s?s:"",parentHeader:n,content:e});this._sendMessage(a)}updateDisplayData(e,t=void 0){var s;const n=void 0!==t?t:this._parentHeader,a=J.KernelMessage.createMessage({channel:"iopub",msgType:"update_display_data",session:null!==(s=null==n?void 0:n.session)&&void 0!==s?s:"",parentHeader:n,content:e});this._sendMessage(a)}clearOutput(e,t=void 0){var s;const n=void 0!==t?t:this._parentHeader,a=J.KernelMessage.createMessage({channel:"iopub",msgType:"clear_output",session:null!==(s=null==n?void 0:n.session)&&void 0!==s?s:"",parentHeader:n,content:e});this._sendMessage(a)}handleComm(e,t,s,n,a=void 0){var r;const i=void 0!==a?a:this._parentHeader,o=J.KernelMessage.createMessage({channel:"iopub",msgType:e,session:null!==(r=null==i?void 0:i.session)&&void 0!==r?r:"",parentHeader:i,content:t,metadata:s,buffers:n});this._sendMessage(o)}_idle(e){const t=J.KernelMessage.createMessage({msgType:"status",session:e.header.session,parentHeader:e.header,channel:"iopub",content:{execution_state:"idle"}});this._sendMessage(t)}_busy(e){const t=J.KernelMessage.createMessage({msgType:"status",session:e.header.session,parentHeader:e.header,channel:"iopub",content:{execution_state:"busy"}});this._sendMessage(t)}async _kernelInfo(e){const t=await this.kernelInfoRequest(),s=J.KernelMessage.createMessage({msgType:"kernel_info_reply",channel:"shell",session:e.header.session,parentHeader:e.header,content:t});this._sendMessage(s)}async _historyRequest(e){const t=e,s=J.KernelMessage.createMessage({msgType:"history_reply",channel:"shell",parentHeader:t.header,session:e.header.session,content:{status:"ok",history:this._history}});this._sendMessage(s)}_executeInput(e){const t=e,s=t.content.code,n=J.KernelMessage.createMessage({msgType:"execute_input",parentHeader:t.header,channel:"iopub",session:e.header.session,content:{code:s,execution_count:this._executionCount}});this._sendMessage(n)}async _execute(e){const t=e,s=t.content;s.store_history&&this._executionCount++,this._parentHeader=t.header,this._executeInput(t),s.store_history&&this._history.push([0,0,s.code]);const n=await this.executeRequest(t.content),a=J.KernelMessage.createMessage({msgType:"execute_reply",channel:"shell",parentHeader:t.header,session:e.header.session,content:n});this._sendMessage(a)}async _complete(e){const t=e,s=await this.completeRequest(t.content),n=J.KernelMessage.createMessage({msgType:"complete_reply",parentHeader:t.header,channel:"shell",session:e.header.session,content:s});this._sendMessage(n)}async _inspect(e){const t=e,s=await this.inspectRequest(t.content),n=J.KernelMessage.createMessage({msgType:"inspect_reply",parentHeader:t.header,channel:"shell",session:e.header.session,content:s});this._sendMessage(n)}async _isCompleteRequest(e){const t=e,s=await this.isCompleteRequest(t.content),n=J.KernelMessage.createMessage({msgType:"is_complete_reply",parentHeader:t.header,channel:"shell",session:e.header.session,content:s});this._sendMessage(n)}}const V=s.p+"pypi/all.json",Z=s.p+"pypi/piplite-0.4.2-py3-none-any.whl";var ee=s(2115);class te extends Q{constructor(e){super(e),this._ready=new Y.PromiseDelegate,this._worker=this.initWorker(e),this._remoteKernel=this.initRemote(e),this._contentsManager=e.contentsManager}initWorker(e){return crossOriginIsolated?new Worker(new URL(s.p+s.u(284),s.b),{type:void 0}):new Worker(new URL(s.p+s.u(903),s.b),{type:void 0})}initRemote(e){let t;crossOriginIsolated?(t=x(this._worker),t.processWorkerMessage=this._processWorkerMessage.bind(this),t.processDriveRequest=async e=>{if(!ee.l)throw new Error("File system calls over Atomics.wait is only supported with jupyterlite>=0.4.0a3");return void 0===this._contentsProcessor&&(this._contentsProcessor=new ee.l({contentsManager:this._contentsManager})),await this._contentsProcessor.processDriveRequest(e)}):(t=S(this._worker),t.registerCallback(z(this._processWorkerMessage.bind(this))));const s=this.initRemoteOptions(e);return t.initialize(s).then(this._ready.resolve.bind(this._ready)),t}initRemoteOptions(e){const{pyodideUrl:t}=e,s=t.slice(0,t.lastIndexOf("/")+1),n=G.PageConfig.getBaseUrl(),a=[...e.pipliteUrls||[],V],r=!!e.disablePyPIFallback;return{baseUrl:n,pyodideUrl:t,indexUrl:s,pipliteWheelUrl:e.pipliteWheelUrl||Z,pipliteUrls:a,disablePyPIFallback:r,location:this.location,mountDrive:e.mountDrive,loadPyodideOptions:e.loadPyodideOptions||{}}}dispose(){this.isDisposed||(this._worker.terminate(),this._worker=null,super.dispose())}get ready(){return this._ready.promise}_processWorkerMessage(e){var t,s,n,a,r,i,o;if(e.type)switch(e.type){case"stream":{const s=null!==(t=e.bundle)&&void 0!==t?t:{name:"stdout",text:""};this.stream(s,e.parentHeader);break}case"input_request":{const t=null!==(s=e.content)&&void 0!==s?s:{prompt:"",password:!1};this.inputRequest(t,e.parentHeader);break}case"display_data":{const t=null!==(n=e.bundle)&&void 0!==n?n:{data:{},metadata:{},transient:{}};this.displayData(t,e.parentHeader);break}case"update_display_data":{const t=null!==(a=e.bundle)&&void 0!==a?a:{data:{},metadata:{},transient:{}};this.updateDisplayData(t,e.parentHeader);break}case"clear_output":{const t=null!==(r=e.bundle)&&void 0!==r?r:{wait:!1};this.clearOutput(t,e.parentHeader);break}case"execute_result":{const t=null!==(i=e.bundle)&&void 0!==i?i:{execution_count:0,data:{},metadata:{}};this.publishExecuteResult(t,e.parentHeader);break}case"execute_error":{const t=null!==(o=e.bundle)&&void 0!==o?o:{ename:"",evalue:"",traceback:[]};this.publishExecuteError(t,e.parentHeader);break}case"comm_msg":case"comm_open":case"comm_close":this.handleComm(e.type,e.content,e.metadata,e.buffers,e.parentHeader)}}async kernelInfoRequest(){return{implementation:"pyodide",implementation_version:"0.1.0",language_info:{codemirror_mode:{name:"python",version:3},file_extension:".py",mimetype:"text/x-python",name:"python",nbconvert_exporter:"python",pygments_lexer:"ipython3",version:"3.8"},protocol_version:"5.3",status:"ok",banner:"A WebAssembly-powered Python kernel backed by Pyodide",help_links:[{text:"Python (WASM) Kernel",url:"https://pyodide.org"}]}}async executeRequest(e){await this.ready;const t=await this._remoteKernel.execute(e,this.parent);return t.execution_count=this.executionCount,t}async completeRequest(e){return await this._remoteKernel.complete(e,this.parent)}async inspectRequest(e){return await this._remoteKernel.inspect(e,this.parent)}async isCompleteRequest(e){return await this._remoteKernel.isComplete(e,this.parent)}async commInfoRequest(e){return await this._remoteKernel.commInfo(e,this.parent)}async commOpen(e){return await this._remoteKernel.commOpen(e,this.parent)}async commMsg(e){return await this._remoteKernel.commMsg(e,this.parent)}async commClose(e){return await this._remoteKernel.commClose(e,this.parent)}async inputReply(e){return await this._remoteKernel.inputReply(e,this.parent)}}}}]);
//# sourceMappingURL=635.thebe-lite.min.js.map