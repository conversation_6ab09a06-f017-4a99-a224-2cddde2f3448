"use strict";(self.webpackChunkthebe_core=self.webpackChunkthebe_core||[]).push([[4704],{94704:(e,t,r)=>{function n(e,t,r){return t(r),r(e,t)}r.r(t),r.d(t,{elm:()=>b});var i=/[a-z]/,o=/[A-Z]/,a=/[a-zA-Z0-9_]/,u=/[0-9]/,f=/[0-9A-Fa-f]/,s=/[-&*+.\\/<>=?^|:]/,l=/[(),[\]{}]/,c=/[ \v\f]/;function p(){return function(e,t){if(e.eatWhile(c))return null;var r=e.next();if(l.test(r))return"{"===r&&e.eat("-")?n(e,t,h(1)):"["===r&&e.match("glsl|")?n(e,t,g):"builtin";if("'"===r)return n(e,t,x);if('"'===r)return e.eat('"')?e.eat('"')?n(e,t,k):"string":n(e,t,m);if(o.test(r))return e.eatWhile(a),"type";if(i.test(r)){var p=1===e.pos;return e.eatWhile(a),p?"def":"variable"}if(u.test(r)){if("0"===r){if(e.eat(/[xX]/))return e.eatWhile(f),"number"}else e.eatWhile(u);return e.eat(".")&&e.eatWhile(u),e.eat(/[eE]/)&&(e.eat(/[-+]/),e.eatWhile(u)),"number"}return s.test(r)?"-"===r&&e.eat("-")?(e.skipToEnd(),"comment"):(e.eatWhile(s),"keyword"):"_"===r?"keyword":"error"}}function h(e){return 0==e?p():function(t,r){for(;!t.eol();){var n=t.next();if("{"==n&&t.eat("-"))++e;else if("-"==n&&t.eat("}")&&0==--e)return r(p()),"comment"}return r(h(e)),"comment"}}function k(e,t){for(;!e.eol();)if('"'===e.next()&&e.eat('"')&&e.eat('"'))return t(p()),"string";return"string"}function m(e,t){for(;e.skipTo('\\"');)e.next(),e.next();return e.skipTo('"')?(e.next(),t(p()),"string"):(e.skipToEnd(),t(p()),"error")}function x(e,t){for(;e.skipTo("\\'");)e.next(),e.next();return e.skipTo("'")?(e.next(),t(p()),"string"):(e.skipToEnd(),t(p()),"error")}function g(e,t){for(;!e.eol();)if("|"===e.next()&&e.eat("]"))return t(p()),"string";return"string"}var d={case:1,of:1,as:1,if:1,then:1,else:1,let:1,in:1,type:1,alias:1,module:1,where:1,import:1,exposing:1,port:1};const b={name:"elm",startState:function(){return{f:p()}},copyState:function(e){return{f:e.f}},token:function(e,t){var r=t.f(e,(function(e){t.f=e})),n=e.current();return d.hasOwnProperty(n)?"keyword":r},languageData:{commentTokens:{line:"--"}}}}}]);
//# sourceMappingURL=4704.thebe-core.min.js.map