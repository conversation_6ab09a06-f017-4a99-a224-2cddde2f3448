"use strict";(self.webpackChunkthebe_core=self.webpackChunkthebe_core||[]).push([[3426],{53426:(e,t,O)=>{O.r(t),O.d(t,{autoCloseTags:()=>A,completeFromSchema:()=>w,xml:()=>Q,xmlLanguage:()=>x});var n=O(73643),r=O(49913);function a(e){return 45==e||46==e||58==e||e>=65&&e<=90||95==e||e>=97&&e<=122||e>=161}let l=null,o=null,s=0;function i(e,t){let O=e.pos+t;if(o==e&&s==O)return l;for(;9==(n=e.peek(t))||10==n||13==n||32==n;)t++;var n;let r="";for(;;){let O=e.peek(t);if(!a(O))break;r+=String.fromCharCode(O),t++}return o=e,s=O,l=r||null}function y(e,t){this.name=e,this.parent=t,this.hash=t?t.hash:0;for(let t=0;t<e.length;t++)this.hash+=(this.hash<<4)+e.charCodeAt(t)+(e.charCodeAt(t)<<8)}const c=new n.Aj({start:null,shift:(e,t,O,n)=>1==t?new y(i(n,1)||"",e):e,reduce:(e,t)=>11==t&&e?e.parent:e,reuse(e,t,O,n){let r=t.type.id;return 1==r||13==r?new y(i(n,1)||"",e):e},hash:e=>e?e.hash:0,strict:!1}),$=new n.Lu(((e,t)=>{if(60==e.next)if(e.advance(),47==e.next){e.advance();let O=i(e,0);if(!O)return e.acceptToken(5);if(t.context&&O==t.context.name)return e.acceptToken(2);for(let n=t.context;n;n=n.parent)if(n.name==O)return e.acceptToken(3,-2);e.acceptToken(4)}else if(33!=e.next&&63!=e.next)return e.acceptToken(1)}),{contextual:!0});function p(e,t){return new n.Lu((O=>{let n=0,r=t.charCodeAt(0);e:for(;!(O.next<0);O.advance(),n++)if(O.next==r){for(let e=1;e<t.length;e++)if(O.peek(e)!=t.charCodeAt(e))continue e;break}n&&O.acceptToken(e)}))}const g=p(35,"--\x3e"),u=p(36,"?>"),m=p(37,"]]>"),S=(0,r.pn)({Text:r._A.content,"StartTag StartCloseTag EndTag SelfCloseEndTag":r._A.angleBracket,TagName:r._A.tagName,"MismatchedCloseTag/TagName":[r._A.tagName,r._A.invalid],AttributeName:r._A.attributeName,AttributeValue:r._A.attributeValue,Is:r._A.definitionOperator,"EntityReference CharacterReference":r._A.character,Comment:r._A.blockComment,ProcessingInst:r._A.processingInstruction,DoctypeDecl:r._A.documentMeta,Cdata:r._A.special(r._A.string)}),f=n.U1.deserialize({version:14,states:",SOQOaOOOrOxO'#CfOzOpO'#CiO!tOaO'#CgOOOP'#Cg'#CgO!{OrO'#CrO#TOtO'#CsO#]OpO'#CtOOOP'#DS'#DSOOOP'#Cv'#CvQQOaOOOOOW'#Cw'#CwO#eOxO,59QOOOP,59Q,59QOOOO'#Cx'#CxO#mOpO,59TO#uO!bO,59TOOOP'#C{'#C{O$TOaO,59RO$[OpO'#CoOOOP,59R,59ROOOQ'#C|'#C|O$dOrO,59^OOOP,59^,59^OOOS'#C}'#C}O$lOtO,59_OOOP,59_,59_O$tOpO,59`O$|OpO,59`OOOP-E6t-E6tOOOW-E6u-E6uOOOP1G.l1G.lOOOO-E6v-E6vO%UO!bO1G.oO%UO!bO1G.oO%dOpO'#CkO%lO!bO'#CyO%zO!bO1G.oOOOP1G.o1G.oOOOP1G.w1G.wOOOP-E6y-E6yOOOP1G.m1G.mO&VOpO,59ZO&_OpO,59ZOOOQ-E6z-E6zOOOP1G.x1G.xOOOS-E6{-E6{OOOP1G.y1G.yO&gOpO1G.zO&gOpO1G.zOOOP1G.z1G.zO&oO!bO7+$ZO&}O!bO7+$ZOOOP7+$Z7+$ZOOOP7+$c7+$cO'YOpO,59VO'bOpO,59VO'jO!bO,59eOOOO-E6w-E6wO'xOpO1G.uO'xOpO1G.uOOOP1G.u1G.uO(QOpO7+$fOOOP7+$f7+$fO(YO!bO<<GuOOOP<<Gu<<GuOOOP<<G}<<G}O'bOpO1G.qO'bOpO1G.qO(eO#tO'#CnOOOO1G.q1G.qO(sOpO7+$aOOOP7+$a7+$aOOOP<<HQ<<HQOOOPAN=aAN=aOOOPAN=iAN=iO'bOpO7+$]OOOO7+$]7+$]OOOO'#Cz'#CzO({O#tO,59YOOOO,59Y,59YOOOP<<G{<<G{OOOO<<Gw<<GwOOOO-E6x-E6xOOOO1G.t1G.t",stateData:")Z~OPQOSVOTWOVWOWWOXWOiXOxPO}TO!PUO~OuZOw]O~O^`Oy^O~OPQOQcOSVOTWOVWOWWOXWOxPO}TO!PUO~ORdO~P!SOseO|gO~OthO!OjO~O^lOy^O~OuZOwoO~O^qOy^O~O[vO`sOdwOy^O~ORyO~P!SO^{Oy^O~OseO|}O~OthO!O!PO~O^!QOy^O~O[!SOy^O~O[!VO`sOd!WOy^O~Oa!YOy^O~Oy^O[mX`mXdmX~O[!VO`sOd!WO~O^!]Oy^O~O[!_Oy^O~O[!aOy^O~O[!cO`sOd!dOy^O~O[!cO`sOd!dO~Oa!eOy^O~Oy^Oz!gO~Oy^O[ma`madma~O[!jOy^O~O[!kOy^O~O[!lO`sOd!mO~OW!pOX!pOz!rO{!pO~O[!sOy^O~OW!pOX!pOz!vO{!pO~O",goto:"%[wPPPPPPPPPPxxP!OP!UPP!_!iP!oxxxP!u!{#R$Z$j$p$v$|PPPP%SXWORYbXRORYb_t`qru!T!U!bQ!h!YS!o!e!fR!t!nQdRRybXSORYbQYORmYQ[PRn[Q_QQkVjp_krz!R!T!X!Z!^!`!f!i!nQr`QzcQ!RlQ!TqQ!XsQ!ZtQ!^{Q!`!QQ!f!YQ!i!]R!n!eQu`S!UqrU![u!U!bR!b!TQ!q!gR!u!qQbRRxbQfTR|fQiUR!OiSXOYTaRb",nodeNames:"⚠ StartTag StartCloseTag MissingCloseTag StartCloseTag StartCloseTag Document Text EntityReference CharacterReference Cdata Element EndTag OpenTag TagName Attribute AttributeName Is AttributeValue CloseTag SelfCloseEndTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag DoctypeDecl",maxTerm:47,context:c,nodeProps:[["closedBy",1,"SelfCloseEndTag EndTag",13,"CloseTag MissingCloseTag"],["openedBy",12,"StartTag StartCloseTag",19,"OpenTag",20,"StartTag"],["isolate",-6,13,18,19,21,22,24,""]],propSources:[S],skippedNodes:[0],repeatNodeCount:8,tokenData:"Jy~R!XOX$nXY&kYZ&kZ]$n]^&k^p$npq&kqr$nrs'ssv$nvw(Zw}$n}!O,^!O!P$n!P!Q.m!Q![$n![!]0V!]!^$n!^!_3h!_!`El!`!aF_!a!bGQ!b!c$n!c!}0V!}#P$n#P#QHj#Q#R$n#R#S0V#S#T$n#T#o0V#o%W$n%W%o0V%o%p$n%p&a0V&a&b$n&b1p0V1p4U$n4U4d0V4d4e$n4e$IS0V$IS$I`$n$I`$Ib0V$Ib$Kh$n$Kh%#t0V%#t&/x$n&/x&Et0V&Et&FV$n&FV;'S0V;'S;:j3b;:j;=`&e<%l?&r$n?&r?Ah0V?Ah?BY$n?BY?Mn0V?MnO$nX$uWVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nP%dTVPOv%_w!^%_!_;'S%_;'S;=`%s<%lO%_P%vP;=`<%l%_W&OT{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yW&bP;=`<%l%yX&hP;=`<%l$n_&t_VP{WyUOX$nXY&kYZ&kZ]$n]^&k^p$npq&kqr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZ'zTzYVPOv%_w!^%_!_;'S%_;'S;=`%s<%lO%_~(^ast)c![!]*g!c!}*g#R#S*g#T#o*g%W%o*g%p&a*g&b1p*g4U4d*g4e$IS*g$I`$Ib*g$Kh%#t*g&/x&Et*g&FV;'S*g;'S;:j,W?&r?Ah*g?BY?Mn*g~)fQ!Q![)l#l#m)z~)oQ!Q![)l!]!^)u~)zOX~~)}R!Q![*W!c!i*W#T#Z*W~*ZS!Q![*W!]!^)u!c!i*W#T#Z*W~*jg}!O*g!O!P*g!Q![*g![!]*g!]!^,R!c!}*g#R#S*g#T#o*g$}%O*g%W%o*g%p&a*g&b1p*g1p4U*g4U4d*g4e$IS*g$I`$Ib*g$Je$Jg*g$Kh%#t*g&/x&Et*g&FV;'S*g;'S;:j,W?&r?Ah*g?BY?Mn*g~,WOW~~,ZP;=`<%l*gZ,eYVP{WOr$nrs%_sv$nw}$n}!O-T!O!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZ-[YVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!a-z!a;'S$n;'S;=`&e<%lO$nZ.TW|QVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n].tYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!a/d!a;'S$n;'S;=`&e<%lO$n]/mWdSVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n_0b!O`S^QVP{WOr$nrs%_sv$nw}$n}!O0V!O!P0V!P!Q$n!Q![0V![!]0V!]!^$n!^!_%y!_!c$n!c!}0V!}#R$n#R#S0V#S#T$n#T#o0V#o$}$n$}%O0V%O%W$n%W%o0V%o%p$n%p&a0V&a&b$n&b1p0V1p4U0V4U4d0V4d4e$n4e$IS0V$IS$I`$n$I`$Ib0V$Ib$Je$n$Je$Jg0V$Jg$Kh$n$Kh%#t0V%#t&/x$n&/x&Et0V&Et&FV$n&FV;'S0V;'S;:j3b;:j;=`&e<%l?&r$n?&r?Ah0V?Ah?BY$n?BY?Mn0V?MnO$n_3eP;=`<%l0VX3mW{WOq%yqr4Vsv%yw!a%y!a!bEU!b;'S%y;'S;=`&_<%lO%yX4[]{WOr%ysv%yw}%y}!O5T!O!f%y!f!g6V!g!}%y!}#O;f#O#W%y#W#XAr#X;'S%y;'S;=`&_<%lO%yX5YV{WOr%ysv%yw}%y}!O5o!O;'S%y;'S;=`&_<%lO%yX5vT}P{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yX6[V{WOr%ysv%yw!q%y!q!r6q!r;'S%y;'S;=`&_<%lO%yX6vV{WOr%ysv%yw!e%y!e!f7]!f;'S%y;'S;=`&_<%lO%yX7bV{WOr%ysv%yw!v%y!v!w7w!w;'S%y;'S;=`&_<%lO%yX7|V{WOr%ysv%yw!{%y!{!|8c!|;'S%y;'S;=`&_<%lO%yX8hV{WOr%ysv%yw!r%y!r!s8}!s;'S%y;'S;=`&_<%lO%yX9SV{WOr%ysv%yw!g%y!g!h9i!h;'S%y;'S;=`&_<%lO%yX9nX{WOr9irs:Zsv9ivw:Zw!`9i!`!a:x!a;'S9i;'S;=`;`<%lO9iP:^TO!`:Z!`!a:m!a;'S:Z;'S;=`:r<%lO:ZP:rOiPP:uP;=`<%l:ZX;PTiP{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yX;cP;=`<%l9iX;kX{WOr%ysv%yw!e%y!e!f<W!f#V%y#V#W?f#W;'S%y;'S;=`&_<%lO%yX<]V{WOr%ysv%yw!f%y!f!g<r!g;'S%y;'S;=`&_<%lO%yX<wV{WOr%ysv%yw!c%y!c!d=^!d;'S%y;'S;=`&_<%lO%yX=cV{WOr%ysv%yw!v%y!v!w=x!w;'S%y;'S;=`&_<%lO%yX=}V{WOr%ysv%yw!c%y!c!d>d!d;'S%y;'S;=`&_<%lO%yX>iV{WOr%ysv%yw!}%y!}#O?O#O;'S%y;'S;=`&_<%lO%yX?VT{WxPOr%ysv%yw;'S%y;'S;=`&_<%lO%yX?kV{WOr%ysv%yw#W%y#W#X@Q#X;'S%y;'S;=`&_<%lO%yX@VV{WOr%ysv%yw#T%y#T#U@l#U;'S%y;'S;=`&_<%lO%yX@qV{WOr%ysv%yw#h%y#h#iAW#i;'S%y;'S;=`&_<%lO%yXA]V{WOr%ysv%yw#T%y#T#U>d#U;'S%y;'S;=`&_<%lO%yXAwV{WOr%ysv%yw#c%y#c#dB^#d;'S%y;'S;=`&_<%lO%yXBcV{WOr%ysv%yw#V%y#V#WBx#W;'S%y;'S;=`&_<%lO%yXB}V{WOr%ysv%yw#h%y#h#iCd#i;'S%y;'S;=`&_<%lO%yXCiV{WOr%ysv%yw#m%y#m#nDO#n;'S%y;'S;=`&_<%lO%yXDTV{WOr%ysv%yw#d%y#d#eDj#e;'S%y;'S;=`&_<%lO%yXDoV{WOr%ysv%yw#X%y#X#Y9i#Y;'S%y;'S;=`&_<%lO%yXE]T!PP{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yZEuWaQVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n_FhW[UVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZGXYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!aGw!a;'S$n;'S;=`&e<%lO$nZHQW!OQVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZHqYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_#P$n#P#QIa#Q;'S$n;'S;=`&e<%lO$nZIhYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!aJW!a;'S$n;'S;=`&e<%lO$nZJaWwQVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n",tokenizers:[$,g,u,m,0,1,2,3],topRules:{Document:[0,6]},tokenPrec:0});var d=O(29587),h=O(14830),_=O(83173);function T(e,t){let O=t&&t.getChild("TagName");return O?e.sliceString(O.from,O.to):""}function V(e,t){let O=t&&t.firstChild;return O&&"OpenTag"==O.name?T(e,O):""}function v(e){for(let t=e&&e.parent;t;t=t.parent)if("Element"==t.name)return t;return null}class P{constructor(e,t,O){this.attrs=t,this.attrValues=O,this.children=[],this.name=e.name,this.completion=Object.assign(Object.assign({type:"type"},e.completion||{}),{label:this.name}),this.openCompletion=Object.assign(Object.assign({},this.completion),{label:"<"+this.name}),this.closeCompletion=Object.assign(Object.assign({},this.completion),{label:"</"+this.name+">",boost:2}),this.closeNameCompletion=Object.assign(Object.assign({},this.completion),{label:this.name+">"}),this.text=e.textContent?e.textContent.map((e=>({label:e,type:"text"}))):[]}}const b=/^[:\-\.\w\u00b7-\uffff]*$/;function W(e){return Object.assign(Object.assign({type:"property"},e.completion||{}),{label:e.name})}function C(e){return"string"==typeof e?{label:`"${e}"`,type:"constant"}:/^"/.test(e.label)?e:Object.assign(Object.assign({},e),{label:`"${e.label}"`})}function w(e,t){let O=[],n=[],r=Object.create(null);for(let e of t){let t=W(e);O.push(t),e.global&&n.push(t),e.values&&(r[e.name]=e.values.map(C))}let a=[],l=[],o=Object.create(null);for(let t of e){let e=n,s=r;t.attributes&&(e=e.concat(t.attributes.map((e=>"string"==typeof e?O.find((t=>t.label==e))||{label:e,type:"property"}:(e.values&&(s==r&&(s=Object.create(s)),s[e.name]=e.values.map(C)),W(e))))));let i=new P(t,e,s);o[i.name]=i,a.push(i),t.top&&l.push(i)}l.length||(l=a);for(let t=0;t<a.length;t++){let O=e[t],n=a[t];if(O.children)for(let e of O.children)o[e]&&n.children.push(o[e]);else n.children=a}return e=>{var t;let{doc:O}=e.state,s=function(e,t){var O;let n=(0,d.mv)(e).resolveInner(t,-1),r=null;for(let e=n;!r&&e.parent;e=e.parent)"OpenTag"!=e.name&&"CloseTag"!=e.name&&"SelfClosingTag"!=e.name&&"MismatchedCloseTag"!=e.name||(r=e);if(r&&(r.to>t||r.lastChild.type.isError)){let e=r.parent;if("TagName"==n.name)return"CloseTag"==r.name||"MismatchedCloseTag"==r.name?{type:"closeTag",from:n.from,context:e}:{type:"openTag",from:n.from,context:v(e)};if("AttributeName"==n.name)return{type:"attrName",from:n.from,context:r};if("AttributeValue"==n.name)return{type:"attrValue",from:n.from,context:r};let O=n==r||"Attribute"==n.name?n.childBefore(t):n;return"StartTag"==(null==O?void 0:O.name)?{type:"openTag",from:t,context:v(e)}:"StartCloseTag"==(null==O?void 0:O.name)&&O.to<=t?{type:"closeTag",from:t,context:e}:"Is"==(null==O?void 0:O.name)?{type:"attrValue",from:t,context:r}:O?{type:"attrName",from:t,context:r}:null}if("StartCloseTag"==n.name)return{type:"closeTag",from:t,context:n.parent};for(;n.parent&&n.to==t&&!(null===(O=n.lastChild)||void 0===O?void 0:O.type.isError);)n=n.parent;return"Element"==n.name||"Text"==n.name||"Document"==n.name?{type:"tag",from:t,context:"Element"==n.name?n:v(n)}:null}(e.state,e.pos);if(!s||"tag"==s.type&&!e.explicit)return null;let{type:i,from:y,context:c}=s;if("openTag"==i){let e=l,t=V(O,c);if(t){let O=o[t];e=(null==O?void 0:O.children)||a}return{from:y,options:e.map((e=>e.completion)),validFor:b}}if("closeTag"==i){let n=V(O,c);return n?{from:y,to:e.pos+(">"==O.sliceString(e.pos,e.pos+1)?1:0),options:[(null===(t=o[n])||void 0===t?void 0:t.closeNameCompletion)||{label:n+">",type:"type"}],validFor:b}:null}if("attrName"==i){let e=o[T(O,c)];return{from:y,options:(null==e?void 0:e.attrs)||n,validFor:b}}if("attrValue"==i){let t=function(e,t,O){let n=t&&t.getChildren("Attribute").find((e=>e.from<=O&&e.to>=O)),r=n&&n.getChild("AttributeName");return r?e.sliceString(r.from,r.to):""}(O,c,y);if(!t)return null;let n=o[T(O,c)],a=((null==n?void 0:n.attrValues)||r)[t];return a&&a.length?{from:y,to:e.pos+('"'==O.sliceString(e.pos,e.pos+1)?1:0),options:a,validFor:/^"[^"]*"?$/}:null}if("tag"==i){let t=V(O,c),n=o[t],r=[],s=c&&c.lastChild;!t||s&&"CloseTag"==s.name&&T(O,s)==t||r.push(n?n.closeCompletion:{label:"</"+t+">",type:"type",boost:2});let i=r.concat(((null==n?void 0:n.children)||(c?a:l)).map((e=>e.openCompletion)));if(c&&(null==n?void 0:n.text.length)){let t=c.firstChild;t.to>e.pos-20&&!/\S/.test(e.state.sliceDoc(t.to,e.pos))&&(i=i.concat(n.text))}return{from:y,options:i,validFor:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}return null}}const x=d.bj.define({name:"xml",parser:f.configure({props:[d.Oh.add({Element(e){let t=/^\s*<\//.test(e.textAfter);return e.lineIndent(e.node.from)+(t?0:e.unit)},"OpenTag CloseTag SelfClosingTag":e=>e.column(e.node.from)+e.unit}),d.b_.add({Element(e){let t=e.firstChild,O=e.lastChild;return t&&"OpenTag"==t.name?{from:t.to,to:"CloseTag"==O.name?O.from:e.to}:null}}),d.Q_.add({"OpenTag CloseTag":e=>e.getChild("TagName")})]}),languageData:{commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}},indentOnInput:/^\s*<\/$/}});function Q(e={}){let t=[x.data.of({autocomplete:w(e.elements||[],e.attributes||[])})];return!1!==e.autoCloseTags&&t.push(A),new d.Yy(x,t)}function X(e,t,O=e.length){if(!t)return"";let n=t.firstChild,r=n&&n.getChild("TagName");return r?e.sliceString(r.from,Math.min(r.to,O)):""}const A=_.Lz.inputHandler.of(((e,t,O,n,r)=>{if(e.composing||e.state.readOnly||t!=O||">"!=n&&"/"!=n||!x.isActiveAt(e.state,t,-1))return!1;let a=r(),{state:l}=a,o=l.changeByRange((e=>{var t,O,r;let a,{head:o}=e,s=l.doc.sliceString(o-1,o)==n,i=(0,d.mv)(l).resolveInner(o,-1);if(s&&">"==n&&"EndTag"==i.name){let n=i.parent;if("CloseTag"!=(null===(O=null===(t=n.parent)||void 0===t?void 0:t.lastChild)||void 0===O?void 0:O.name)&&(a=X(l.doc,n.parent,o)))return{range:e,changes:{from:o,to:o+(">"===l.doc.sliceString(o,o+1)?1:0),insert:`</${a}>`}}}else if(s&&"/"==n&&"StartCloseTag"==i.name){let e=i.parent;if(i.from==o-2&&"CloseTag"!=(null===(r=e.lastChild)||void 0===r?void 0:r.name)&&(a=X(l.doc,e,o))){let e=o+(">"===l.doc.sliceString(o,o+1)?1:0),t=`${a}>`;return{range:h.OF.cursor(o+t.length,-1),changes:{from:o,to:e,insert:t}}}}return{range:e}}));return!o.changes.empty&&(e.dispatch([a,l.update(o,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}))}}]);
//# sourceMappingURL=3426.thebe-core.min.js.map