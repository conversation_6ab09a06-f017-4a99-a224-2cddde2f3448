"use strict";(self.webpackChunkthebe_core=self.webpackChunkthebe_core||[]).push([[4922],{94922:(e,t,n)=>{n.r(t),n.d(t,{shell:()=>k});var r={};function s(e,t){for(var n=0;n<t.length;n++)r[t[n]]=e}var i=["true","false"],o=["if","then","do","else","elif","while","until","for","in","esac","fi","fin","fil","done","exit","set","unset","export","function"],u=["ab","awk","bash","beep","cat","cc","cd","chown","chmod","chroot","clear","cp","curl","cut","diff","echo","find","gawk","gcc","get","git","grep","hg","kill","killall","ln","ls","make","mkdir","openssl","mv","nc","nl","node","npm","ping","ps","restart","rm","rmdir","sed","service","sh","shopt","shred","source","sort","sleep","ssh","start","stop","su","sudo","svn","tee","telnet","top","touch","vi","vim","wall","wc","wget","who","write","yes","zsh"];function a(e,t){if(e.eatSpace())return null;var n,s=e.sol(),i=e.next();if("\\"===i)return e.next(),null;if("'"===i||'"'===i||"`"===i)return t.tokens.unshift(c(i,"`"===i?"quote":"string")),h(e,t);if("#"===i)return s&&e.eat("!")?(e.skipToEnd(),"meta"):(e.skipToEnd(),"comment");if("$"===i)return t.tokens.unshift(l),h(e,t);if("+"===i||"="===i)return"operator";if("-"===i)return e.eat("-"),e.eatWhile(/\w/),"attribute";if("<"==i){if(e.match("<<"))return"operator";var o=e.match(/^<-?\s*(?:['"]([^'"]*)['"]|([^'"\s]*))/);if(o)return t.tokens.unshift((n=o[1]||o[2],function(e,t){return e.sol()&&e.string==n&&t.tokens.shift(),e.skipToEnd(),"string.special"})),"string.special"}if(/\d/.test(i)&&(e.eatWhile(/\d/),e.eol()||!/\w/.test(e.peek())))return"number";e.eatWhile(/[\w-]/);var u=e.current();return"="===e.peek()&&/\w+/.test(u)?"def":r.hasOwnProperty(u)?r[u]:null}function c(e,t){var n="("==e?")":"{"==e?"}":e;return function(r,s){for(var i,o=!1;null!=(i=r.next());){if(i===n&&!o){s.tokens.shift();break}if("$"===i&&!o&&"'"!==e&&r.peek()!=n){o=!0,r.backUp(1),s.tokens.unshift(l);break}if(!o&&e!==n&&i===e)return s.tokens.unshift(c(e,t)),h(r,s);if(!o&&/['"]/.test(i)&&!/['"]/.test(e)){s.tokens.unshift(f(i,"string")),r.backUp(1);break}o=!o&&"\\"===i}return t}}function f(e,t){return function(n,r){return r.tokens[0]=c(e,t),n.next(),h(n,r)}}s("atom",i),s("keyword",o),s("builtin",u);var l=function(e,t){t.tokens.length>1&&e.eat("$");var n=e.next();return/['"({]/.test(n)?(t.tokens[0]=c(n,"("==n?"quote":"{"==n?"def":"string"),h(e,t)):(/\d/.test(n)||e.eatWhile(/\w/),t.tokens.shift(),"def")};function h(e,t){return(t.tokens[0]||a)(e,t)}const k={name:"shell",startState:function(){return{tokens:[]}},token:function(e,t){return h(e,t)},languageData:{autocomplete:i.concat(o,u),closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"#"}}}}}]);
//# sourceMappingURL=4922.thebe-core.min.js.map