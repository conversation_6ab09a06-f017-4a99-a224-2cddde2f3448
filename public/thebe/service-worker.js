"use strict";const CACHE="precache",broadcast=new BroadcastChannel("/api/drive.v1");let enableCache=!1;function onInstall(a){self.skipWaiting(),a.waitUntil(cacheAll())}function onActivate(a){const e=new URL(location.href).searchParams;enableCache="true"===e.get("enableCache"),a.waitUntil(self.clients.claim())}async function onFetch(a){const{request:e}=a,t=new URL(a.request.url);let n=null;shouldBroadcast(t)?n=broadcastOne(e):shouldDrop(e,t)||(n=maybeFromCache(a)),n&&a.respondWith(n)}async function maybeFromCache(a){const{request:e}=a;if(!enableCache)return await fetch(e);let t=await fromCache(e);return t?a.waitUntil(refetch(e)):(t=await fetch(e),a.waitUntil(updateCache(e,t.clone()))),t}async function fromCache(a){const e=await openCache(),t=await e.match(a);return t&&404!==t.status?t:null}async function refetch(a){const e=await fetch(a);return await updateCache(a,e),e}function shouldBroadcast(a){return a.origin===location.origin&&a.pathname.includes("/api/drive")}function shouldDrop(a,e){return"GET"!==a.method||null===e.origin.match(/^http/)||e.pathname.includes("/api/")}async function broadcastOne(a){const e=new Promise((a=>{broadcast.onmessage=e=>{a(new Response(JSON.stringify(e.data)))}})),t=await a.json();return t.receiver="broadcast.ts",broadcast.postMessage(t),await e}async function openCache(){return await caches.open(CACHE)}async function updateCache(a,e){return(await openCache()).put(a,e)}async function cacheAll(){const a=await openCache();return await a.addAll([])}self.addEventListener("install",onInstall),self.addEventListener("activate",onActivate),self.addEventListener("fetch",onFetch);