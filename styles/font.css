/* AlibabaPuHuiTi-3-115-Black */
@font-face {
    font-family: 'AlibabaPuHuiTi-Black';
    src: url('./fonts/AlibabaPuHuiTi-3-115-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
}

/* AlibabaPuHuiTi-3-35-Thin */
@font-face {
    font-family: 'AlibabaPuHuiTi-Thin';
    src: url('./fonts/AlibabaPuHuiTi-3-35-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}

/* AlibabaPuHuiTi-3-55-RegularL3 */
@font-face {
    font-family: 'AlibabaPuHuiTi-RegularL3';
    src: url('./fonts/AlibabaPuHuiTi-3-55-RegularL3.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* AlibabaPuHuiTi-3-85-Bold */
@font-face {
    font-family: 'AlibabaPuHuiTi-Bold';
    src: url('./fonts/AlibabaPuHuiTi-3-85-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

/* AlimamaAgileVF-Thin */
@font-face {
    font-family: 'AlimamaAgileVF-Thin';
    src: url('./fonts/AlimamaAgileVF-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}

/* AlimamaFangYuanTiVF-Thin */
@font-face {
    font-family: 'AlimamaFangYuanTiVF-Thin';
    src: url('./fonts/AlimamaFangYuanTiVF-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}

/* OPPOSans-Bold */
@font-face {
    font-family: 'OPPOSans-Bold';
    src: url('./fonts/OPPOSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

/* OPPOSans-Medium */
@font-face {
    font-family: 'OPPOSans-Medium';
    src: url('./fonts/OPPOSans-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
}

/* OPPOSans-Regular */
@font-face {
    font-family: 'OPPOSans-Regular';
    src: url('./fonts/OPPOSans-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* WenQuanWeiMiHei */
@font-face {
    font-family: 'WenQuanWeiMiHei';
    src: url('./fonts/WenQuanWeiMiHei.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* ZhuqueFangsong-Regular */
@font-face {
    font-family: 'ZhuqueFangsong';
    src: url('./fonts/ZhuqueFangsong-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* wqy-microhei-lite */
@font-face {
    font-family: 'wqy-microhei-lite';
    src: url('./fonts/WenQuanYiMicroHeiLight.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* wqy-zenhei */
@font-face {
    font-family: 'WenQuanYiZenHei';
    src: url('./fonts/WenQuanYiZenHei.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'WenQuanYiZenHeiMono';
    src: url('./fonts/WenQuanYiZenHeiMono.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 庞门正道标题体免费版 */
@font-face {
    font-family: 'PangMenTitle';
    src: url('./fonts/庞门正道标题体免费版_mianfeiziti.com.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 庞门正道粗书体 */
@font-face {
    font-family: 'PangMenChuShuTi';
    src: url('./fonts/庞门正道粗书体_mianfeiziti.com.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 演示夏行 */
@font-face {
    font-family: 'YanShiXiaXing';
    src: url('./fonts/演示夏行.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 演示春风-Regular */
@font-face {
    font-family: 'YanShiChunFeng';
    src: url('./fonts/演示春风-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 演示秋鸿-Regular */
@font-face {
    font-family: 'YanShiQiuHong';
    src: url('./fonts/演示秋鸿-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 辰余落雁细-等宽 */
@font-face {
    font-family: 'ChenYuLuoYanXi';
    src: url('./fonts/辰余落雁细-等宽.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 青柳隶书 */
@font-face {
    font-family: 'QingLiuLiShu';
    src: url('./fonts/青柳隶书_mianfeiziti.com.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 鸿雷板书简体-正式版 */
@font-face {
    font-family: 'HongLeiBanShuJianTi';
    src: url('./fonts/鸿雷板书简体-正式版.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}

/* 鸿雷行书简体 */
@font-face {
    font-family: 'HongLeiXingShuJianTi';
    src: url('./fonts/鸿雷行书简体.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}
