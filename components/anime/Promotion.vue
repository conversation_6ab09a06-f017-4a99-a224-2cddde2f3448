<script setup>
import { computed } from 'vue'
const props = defineProps({
    at: {
        type: Number,
        default: -1
    },
    dur: {
        type: Number,
        default: 3
    },
    left: {
        type: String,
        default: '30%'
    },
    w: {
        type: String,
        default: '60%'
    },
    perspective: {
        type: String,
        default: '1000px'
    },
    perspective_origin: {
        type: String,
        default: '50%'
    }
})

const show = computed(() => {
    if (Array.isArray(props.at)) {
        return props.at.includes($slidev.nav.clicks)
    } else {
        return [-1, $slidev.nav.clicks].includes(props.at)
    }
})
</script>
<template>
    <!--promotion-->
    <Cast v-if="show" :dur="props.dur" :perspective="props.perspective" left="props.left" w="props.w"
        :perspective_origin="props.perspective_origin">
        <div style="text-align:center;">
            <div
                style="padding:100px 20px;background: url('https://images.jieyu.ai/images/hot/black-gold.jpg') repeat-y center top / contain">

                《大富翁.量化二十四课》

                <h3>视频</h3>

                <video src="https://images.jieyu.ai/images/hot/course/video-sample.mp4" preload autoplay loop></video>

                <h3>Notebook文稿和代码</h3>

                <img src="https://images.jieyu.ai/images/hot/course/course-screenshot.jpg" />

                <h3>1对1指导</h3>

                量化投研系统专家、大富翁架构师、《Python高效编程实践指南》作者一对一指导。

                <div style="width:100%">
                    <img src="https://images.jieyu.ai/images/hot/mybook/book-cover-no-author.jpg"
                        style="float:left;width:200px">


                </div>

                ✥ 超翔实内容：文字稿约40万字节。<br>
                ✥ 快速上手：环境基于Jupyter Lab构建，在线使用，无须安装和拷贝数据。<br>
                ✥ 昂贵的商业数据：超过30亿条分钟级行情数据<br>
                ✥ 贴近实战：提供真实数据回测环境<br>
                ✥ 豪华环境：192核CPU和256GB内存（学员共享）<br>
                <slot></slot>
            </div>
        </div>
    </Cast>
</template>
